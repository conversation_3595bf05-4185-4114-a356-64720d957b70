"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MoreHorizontal, Play, PauseCircle } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { useDeleteMusic } from "@schemas/Music/music-query";
import { toast } from "sonner";
import { GetMusics_ResponseTypeSuccess } from "@schemas/Music/music-query";
import { useState } from "react";
import { MusicFormSheet } from "./music-form-sheet";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useAudioStore } from "@/lib/audio-store";
import { TableCell, TableRow } from "@/components/ui/table";
import { formatDate } from "@/lib/utils";

interface MusicRowProps {
  music: GetMusics_ResponseTypeSuccess[number];
}

export function MusicRow({ music }: MusicRowProps) {
  const router = useRouter();
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
  const { mutateAsync: deleteMusic } = useDeleteMusic();
  const { selectedAudioId, setSelectedAudioId } = useAudioStore();

  const isPlaying = selectedAudioId === music.id;

  const handleDelete = async () => {
    try {
      await deleteMusic({ id: music.id });
      toast.success("Music deleted successfully");
      router.refresh();
    } catch {
      toast.error("Failed to delete music");
    }
  };

  const handlePlayToggle = () => {
    if (isPlaying) {
      setSelectedAudioId("");
    } else {
      setSelectedAudioId(music.id);
    }
  };

  return (
    <>
      <TableRow>
        <TableCell className="font-medium">{music.title}</TableCell>
        <TableCell>{music.source}</TableCell>

        <TableCell>
          {music.rating ? (
            <div className="flex items-center gap-1">
              <span>{music.rating.toFixed(1)}</span>
              <span className="text-yellow-500">★</span>
            </div>
          ) : (
            <span className="text-muted-foreground">N/A</span>
          )}
        </TableCell>
        <TableCell>
          <div className="flex flex-wrap gap-1">
            {music.genres.map((genre, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {genre}
              </Badge>
            ))}
          </div>
        </TableCell>
        <TableCell>
          <Badge variant={music.isPublic ? "default" : "secondary"}>
            {music.isPublic ? "Public" : "Private"}
          </Badge>
        </TableCell>
        <TableCell>
          <Badge variant={music.isCopyright ? "destructive" : "outline"}>
            {music.isCopyright ? "Copyrighted" : "Free"}
          </Badge>
        </TableCell>
        <TableCell>
          <Badge variant="outline" className="capitalize">
            {music.creatorType.toLowerCase()}
          </Badge>
        </TableCell>
        <TableCell>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handlePlayToggle}
              className="h-8 w-8"
            >
              {isPlaying ? (
                <PauseCircle className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5" />
              )}
            </Button>

            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setIsEditSheetOpen(true)}>
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <DropdownMenuItem
                      onSelect={(e) => e.preventDefault()}
                      className="text-destructive"
                    >
                      Delete
                    </DropdownMenuItem>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Music</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete this music track? This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDelete}
                        className="bg-destructive hover:bg-destructive/90"
                      >
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </TableCell>
      </TableRow>

      <MusicFormSheet
        open={isEditSheetOpen}
        onOpenChange={setIsEditSheetOpen}
        musicId={music.id}
      />
    </>
  );
}