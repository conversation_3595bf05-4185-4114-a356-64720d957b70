"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

interface FocusDistributionProps {
  data: Array<{
    hour: string
    value: number
  }>
}

interface CustomTooltipProps {
  active?: boolean
  payload?: Array<{ value: number }>
  label?: string
}

export function FocusDistribution({ data }: FocusDistributionProps) {
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-md border border-border bg-card p-3 shadow-md">
          <p className="mb-1 font-medium">{label}</p>
          <p className="text-primary">
            <span className="font-bold">{payload[0].value}</span> minutes
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 10, left: 0, bottom: 20 }}>
          <XAxis
            dataKey="hour"
            stroke="currentColor"
            className="text-muted-foreground"
            tickLine={false}
            axisLine={false}
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={50}
          />
          <YAxis 
            stroke="currentColor" 
            className="text-muted-foreground" 
            tickLine={false} 
            axisLine={false} 
            tick={{ fontSize: 12 }} 
            width={30} 
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="value" fill="#5576F5" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
