import { TimerColorPreset, TimerUIStyle } from '@/lib/pomodoro-store';
import { TimerFormValues } from '../timer';

export interface TabComponentProps {
  form: any; // react-hook-form form
  handleNumberChange: (field: keyof TimerFormValues, value: number) => void;
  pomodoroValue: number;
  shortBreakValue: number;
  longBreakValue: number;
  sessionsValue: number;
  calculateTotalDuration: (
    pomodoroMinutes: number,
    shortBreakMinutes: number,
    longBreakMinutes: number,
    sessionsCount: number
  ) => string;
  calculateEndTime: (
    pomodoroMinutes: number,
    shortBreakMinutes: number,
    longBreakMinutes: number,
    sessionsCount: number
  ) => string;
}

export interface AppearanceTabProps {
  timerColor: TimerColorPreset;
  setTimerColor: (color: TimerColorPreset) => void;
  timerOpacity: number;
  handleOpacityChange: (value: number) => void;
  timerUIStyle: TimerUIStyle;
  setTimerUIStyle: (style: TimerUIStyle) => void;
}

export interface ControlsTabProps {
  autoStartBreaks: boolean;
  setAutoStartBreaks: (value: boolean) => void;
  autoStartPomodoros: boolean;
  setAutoStartPomodoros: (value: boolean) => void;
  autoFullscreen: boolean;
  setAutoFullscreen: (value: boolean) => void;
}


export interface TimerSettingsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  noDimmer?: boolean;
}
