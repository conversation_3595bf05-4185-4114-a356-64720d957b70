// Updated MediaTabs component for the music-playlist-id route
// This component manages the videos and music tabs in a music playlist
// With improved TypeScript support and fixed references

import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Play,
  Plus,
  Music2,
  Video,
  MoreHorizontal,
  Volume2,
  Pause,
  ChevronUp,
  ChevronDown,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { GetAdminMusicPlaylist_ResponseTypeSuccess } from "@schemas/MusicPlaylist/music-playlist-admin-query";
import { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "sonner";
import { 
  useRemoveVideoFromAdminMusicPlaylist, 
  useRemoveMusicFromAdminMusicPlaylist,
  useReorderMusicInAdminMusicPlaylist
} from "@schemas/MusicPlaylist/music-playlist-admin-query";

interface Music {
  id: string;
  title: string;
  artist?: string;
  src: string | null;
}

interface Video {
  id: string;
  title: string;
  thumbnail: string;
  description?: string | null;
  src: string;
  musicPlaylistId?: string | null;
  naturePlaylistId?: string | null;
}

interface MediaTabsProps {
  musicPlaylist: GetAdminMusicPlaylist_ResponseTypeSuccess;
  activeTab: string;
  setActiveTab: (value: string) => void;
  onAddMusic: () => void;
  onAddVideo: () => void;
  onRefetch: () => void;
}

export function MediaTabs({
  musicPlaylist,
  activeTab,
  setActiveTab,
  onAddMusic,
  onAddVideo,
  onRefetch
}: MediaTabsProps) {
  const router = useRouter();
  const [videoFilter, setVideoFilter] = useState("");
  const [musicFilter, setMusicFilter] = useState("");
  const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Mutations for removing media from playlist
  const removeVideo = useRemoveVideoFromAdminMusicPlaylist();
  const removeMusic = useRemoveMusicFromAdminMusicPlaylist();

  // Add reorder mutation
  const reorderMusic = useReorderMusicInAdminMusicPlaylist();

  // Filter videos based on search input
  const filteredVideos = musicPlaylist.videos.filter((video: Video) =>
    video.title.toLowerCase().includes(videoFilter.toLowerCase())
  );

  // Sort music based on musicOrder
  const sortedMusic = [...musicPlaylist.musics].sort((a, b) => {
    const orderA = musicPlaylist.musicOrder?.indexOf(a.id) ?? -1;
    const orderB = musicPlaylist.musicOrder?.indexOf(b.id) ?? -1;
    return orderA - orderB;
  });

  // Filter music based on search input (using sorted music)
  const filteredMusic = sortedMusic.filter((music: Music) =>
    music.title.toLowerCase().includes(musicFilter.toLowerCase())
  );

  // Stop any playing audio when switching tabs
  const handleTabChange = (value: string) => {
    if (audioRef.current && currentlyPlaying) {
      audioRef.current.pause();
      setCurrentlyPlaying(null);
    }
    setActiveTab(value);
  };

  // Handler for removing a video from the playlist
  const handleRemoveVideo = (videoId: string) => {
    if (musicPlaylist.isDefault) {
      toast.error("Cannot modify default playlists");
      return;
    }
    
    removeVideo.mutate(
      { musicPlaylistId: musicPlaylist.id, videoId },
      {
        onSuccess: () => {
          toast.success("Video removed from playlist");
          onRefetch();
        }
      }
    );
  };

  // Handler for removing a music track from the playlist
  const handleRemoveMusic = (musicId: string) => {
    if (musicPlaylist.isDefault) {
      toast.error("Cannot modify default playlists");
      return;
    }
    
    removeMusic.mutate(
      { musicPlaylistId: musicPlaylist.id, musicId },
      {
        onSuccess: () => {
          toast.success("Music removed from playlist");
          onRefetch();
        }
      }
    );
  };

  // Handle audio playback
  const handlePlayMusic = (music: Music) => {
    if (currentlyPlaying === music.id) {
      audioRef.current?.pause();
      setCurrentlyPlaying(null);
    } else {
      if (audioRef.current && music.src) {
        audioRef.current.src = music.src;
        
        // Use setTimeout to ensure the audio element is fully rendered
        setTimeout(() => {
          audioRef.current?.play()
            .catch(error => {
              console.error("Error playing audio:", error);
              toast.error("Could not play this audio track");
            });
        }, 50);
        
        setCurrentlyPlaying(music.id);
      } else if (!music.src) {
        toast.error("This track doesn't have a valid audio source");
      }
    }
  };

  // Function to handle moving music up/down
  const handleMoveMusic = (musicId: string, direction: 'up' | 'down') => {
    if (musicPlaylist.isDefault) {
      toast.error("Cannot modify default playlists");
      return;
    }

    const currentOrder = musicPlaylist.musicOrder || [];
    const currentIndex = currentOrder.indexOf(musicId);
    
    if (currentIndex === -1) return;

    const newOrder = [...currentOrder];
    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

    // Check if move is valid
    if (newIndex < 0 || newIndex >= newOrder.length) return;

    // Swap positions
    [newOrder[currentIndex], newOrder[newIndex]] = [newOrder[newIndex], newOrder[currentIndex]];

    reorderMusic.mutate(
      { musicPlaylistId: musicPlaylist.id, musicOrder: newOrder },
      {
        onSuccess: () => {
          toast.success("Music order updated");
          onRefetch();
        }
      }
    );
  };

  // Audio player controls component
  const AudioControls = ({ isPlaying }: { isPlaying: boolean }) => {
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [volume, setVolume] = useState(1);

    useEffect(() => {
      const audio = audioRef.current;
      if (!audio) return;

      const updateTime = () => {
        setCurrentTime(audio.currentTime);
        setDuration(audio.duration);
      };

      audio.addEventListener('timeupdate', updateTime);
      audio.addEventListener('loadedmetadata', updateTime);
      audio.addEventListener('ended', () => setCurrentlyPlaying(null));

      return () => {
        audio.removeEventListener('timeupdate', updateTime);
        audio.removeEventListener('loadedmetadata', updateTime);
        audio.removeEventListener('ended', () => setCurrentlyPlaying(null));
      };
    }, []);

    const handleVolumeChange = (newVolume: number) => {
      if (audioRef.current) {
        audioRef.current.volume = newVolume;
        setVolume(newVolume);
      }
    };

    const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
      if (!audioRef.current || !duration) return;

      const progressBar = e.currentTarget;
      const rect = progressBar.getBoundingClientRect();
      const pos = (e.clientX - rect.left) / rect.width;
      const newTime = pos * duration;

      if (audioRef.current) {
        audioRef.current.currentTime = newTime;
        setCurrentTime(newTime);
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (!audioRef.current) return;

      if (e.key === 'ArrowRight') {
        audioRef.current.currentTime += 5; // Forward 5 seconds
      } else if (e.key === 'ArrowLeft') {
        audioRef.current.currentTime -= 5; // Backward 5 seconds
      } else if (e.key === ' ' || e.key === 'Space') {
        e.preventDefault();
        if (audioRef.current.paused) {
          audioRef.current.play().catch(() => {});
        } else {
          audioRef.current.pause();
        }
      }
    };

    if (!isPlaying) return null;

    return (
      <div 
        className="fixed bottom-0 left-0 right-0 bg-background border-t p-2 flex flex-col gap-1 z-50"
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        {/* Progress bar */}
        <div 
          className="h-1 bg-secondary w-full cursor-pointer rounded-full overflow-hidden"
          onClick={handleProgressClick}
        >
          <div 
            className="h-full bg-primary rounded-full"
            style={{ width: `${(currentTime / duration) * 100}%` }}
          />
        </div>
        
        <div className="flex justify-between items-center px-2">
          {/* Time display */}
          <div className="text-xs text-muted-foreground">
            {formatTime(currentTime)} / {formatTime(duration)}
          </div>
          
          {/* Volume control */}
          <div className="flex items-center gap-2">
            <Volume2 className="h-3 w-3 text-muted-foreground" />
            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              value={volume}
              onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
              className="w-20 h-1"
            />
          </div>
        </div>
      </div>
    );
  };

  const formatTime = (time: number): string => {
    if (isNaN(time)) return "0:00";
    
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  // Empty state component
  const EmptyState = ({
    type,
    onAdd
  }: {
    type: "videos" | "music",
    onAdd?: () => void
  }) => {
    let title = "";
    let icon = null;
    let description = "";

    switch(type) {
      case "videos":
        title = "No Videos Added";
        icon = <Video className="h-12 w-12 text-muted-foreground/50" />;
        description = "Add videos to this playlist to see them here.";
        break;
      case "music":
        title = "No Music Added";
        icon = <Music2 className="h-12 w-12 text-muted-foreground/50" />;
        description = "Add music tracks to this playlist to see them here.";
        break;
      default:
        break;
    }

    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="bg-muted rounded-full p-4 mb-4">
          {icon}
        </div>
        <h3 className="text-lg font-medium mb-2">{title}</h3>
        <p className="text-sm text-muted-foreground mb-6 max-w-md">
          {description}
        </p>
        {onAdd && (
          <Button onClick={onAdd} size="sm" className="gap-1">
            <Plus className="h-4 w-4" />
            Add {type === "videos" ? "Videos" : type === "music" ? "Music" : ""}
          </Button>
        )}
      </div>
    );
  };

  return (
    <>
      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <div className="flex items-center justify-between mb-4">
          <TabsList>
          <TabsTrigger value="music" className="gap-1">
              <Music2 className="h-4 w-4" />
              <span className="hidden sm:inline">Music</span>
              <Badge variant="secondary" className="ml-1 text-xs">
                {musicPlaylist.musics.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="videos" className="gap-1">
              <Video className="h-4 w-4" />
              <span className="hidden sm:inline">Videos</span>
              <Badge variant="secondary" className="ml-1 text-xs">
                {musicPlaylist.videos.length}
              </Badge>
            </TabsTrigger>

          </TabsList>

          {activeTab === "videos" && (
            <Button size="sm" className="gap-1" onClick={onAddVideo}>
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Add Videos</span>
            </Button>
          )}
          {activeTab === "music" && (
            <Button size="sm" className="gap-1" onClick={onAddMusic}>
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Add Music</span>
            </Button>
          )}
        </div>

        <TabsContent value="videos" className="p-0 border-0">
          {musicPlaylist.videos.length === 0 ? (
            <EmptyState type="videos" onAdd={onAddVideo} />
          ) : (
            <div>
              {/* Search input for videos */}
              <div className="mb-6">
                <Input
                  placeholder="Search videos..."
                  value={videoFilter}
                  onChange={(e) => setVideoFilter(e.target.value)}
                  className="max-w-sm"
                />
              </div>

              {/* Videos Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredVideos.map((video) => (
                  <Card key={video.id} className="overflow-hidden">
                    <div className="relative aspect-video">
                      {video.thumbnail ? (
                        <Image
                          src={video.thumbnail}
                          alt={video.title}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                        />
                      ) : (
                        <div className="absolute inset-0 bg-muted flex items-center justify-center">
                          <Video className="h-12 w-12 text-muted-foreground/50" />
                        </div>
                      )}
                      
                      <Button
                        variant="default"
                        size="icon"
                        className="absolute inset-0 m-auto bg-black/30 backdrop-blur-[2px] rounded-full h-12 w-12 hover:bg-black/50"
                        onClick={() => {
                          // Implement playback logic or redirection
                          if (video.src) {
                            router.push(`/admin/videos/${video.id}`);
                          } else {
                            toast.error("This video doesn't have a valid source");
                          }
                        }}
                      >
                        <Play className="h-6 w-6" />
                      </Button>
                      
                      <DropdownMenu modal={false}>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="absolute top-2 right-2 bg-black/20 backdrop-blur-[1px] text-white hover:bg-black/40 h-8 w-8"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem
                            onClick={() => router.push(`/admin/videos/${video.id}`)}
                          >
                            View details
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => handleRemoveVideo(video.id)}
                          >
                            Remove from playlist
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    
                    <CardContent className="p-3">
                      <h3 className="font-medium text-sm line-clamp-1">{video.title}</h3>
                      {video.description && (
                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {video.description}
                        </p>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="music" className="p-0 border-0">
          {musicPlaylist.musics.length === 0 ? (
            <EmptyState type="music" onAdd={onAddMusic} />
          ) : (
            <div>
              {/* Search input for music */}
              <div className="mb-6">
                <Input
                  placeholder="Search music tracks..."
                  value={musicFilter}
                  onChange={(e) => setMusicFilter(e.target.value)}
                  className="max-w-sm"
                />
              </div>

              {/* Music list */}
              <div className="space-y-2">
                {filteredMusic.map((music) => (
                  <div
                    key={music.id}
                    className={`flex items-center gap-4 p-2 rounded-md ${
                      currentlyPlaying === music.id
                        ? "bg-primary/10 dark:bg-primary/20"
                        : "hover:bg-muted/50"
                    }`}
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`shrink-0 h-10 w-10 rounded-full ${
                        currentlyPlaying === music.id
                          ? "text-primary bg-primary/20 hover:bg-primary/30"
                          : ""
                      }`}
                      onClick={() => handlePlayMusic(music)}
                    >
                      {currentlyPlaying === music.id ? (
                        <Pause className="h-5 w-5" />
                      ) : (
                        <Play className="h-5 w-5" />
                      )}
                    </Button>

                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium line-clamp-1">{music.title}</h3>
                    </div>

                    <div className="flex items-center gap-1">
                      {/* Reorder buttons */}
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-muted-foreground"
                              onClick={() => handleMoveMusic(music.id, "up")}
                              disabled={musicPlaylist?.musicOrder?.indexOf(music.id) === 0}
                            >
                              <ChevronUp className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Move up</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-muted-foreground"
                              onClick={() => handleMoveMusic(music.id, "down")}
                              disabled={
                                musicPlaylist?.musicOrder &&
                                musicPlaylist.musicOrder.indexOf(music.id) === musicPlaylist.musicOrder.length - 1
                              }
                            >
                              <ChevronDown className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Move down</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <DropdownMenu modal={false}>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-muted-foreground"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem
                            onClick={() => router.push(`/admin/music/${music.id}`)}
                          >
                            View details
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => handleRemoveMusic(music.id)}
                          >
                            Remove from playlist
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Hidden audio element for playback */}
      <audio ref={audioRef} className="hidden" />

      {/* Audio controls */}
      <AudioControls isPlaying={!!currentlyPlaying} />
    </>
  );
}