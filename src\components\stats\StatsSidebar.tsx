"use client"

import {
  <PERSON><PERSON>hart<PERSON>,
  <PERSON>,
  Calendar,
  Clock,
  CalendarDays,
  Sparkles,
  Timer
} from "lucide-react"
import { cn } from "@/lib/utils"
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Badge } from "@/components/ui/badge"

interface StatsSidebarProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

export function StatsSidebar({ activeTab, onTabChange }: StatsSidebarProps) {

  // Navigation items with modern icons and descriptions
  const navigationItems = [
    {
      title: "Overview",
      icon: BarChart3,
      value: "overview",
      description: "Key metrics"
    },
    {
      title: "Daily Patterns",
      icon: Clock,
      value: "daily",
      description: "Focus trends"
    },
    {
      title: "Calendar",
      icon: Calendar,
      value: "calendar",
      description: "Activity view"
    },
    {
      title: "AI Insights",
      icon: Brain,
      value: "insights",
      description: "Smart analysis",
      badge: "AI"
    },
    {
      title: "Daily View",
      icon: CalendarDays,
      value: "daily-view",
      description: "Session timeline"
    }
  ]

  return (
    <Sidebar variant="inset" className="border-r border-border/40 bg-background">
      {/* Compact Header */}
      <SidebarHeader className="border-b border-border/20 px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="flex h-7 w-7 items-center justify-center rounded-md bg-gradient-to-br from-primary/20 to-primary/10 ring-1 ring-primary/20">
              <Timer className="h-3.5 w-3.5 text-primary" />
            </div>
            <div>
              <h2 className="text-sm font-semibold tracking-tight">
                Pomodoro <span className="text-primary/80">365</span>
              </h2>
            </div>
          </div>
          <SidebarTrigger className="h-6 w-6 text-muted-foreground hover:text-foreground" />
        </div>
      </SidebarHeader>

      {/* Navigation */}
      <SidebarContent className="px-3 py-2">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {navigationItems.map((item) => (
                <SidebarMenuItem key={item.value}>
                  <SidebarMenuButton
                    onClick={() => onTabChange(item.value)}
                    isActive={activeTab === item.value}
                    className={cn(
                      "w-full justify-start gap-2.5 px-2.5 py-2 text-sm group relative",
                      "hover:bg-accent/60 hover:text-accent-foreground",
                      "data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium",
                      "data-[active=true]:shadow-sm",
                      "transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]",
                      "focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:ring-offset-1",
                      "rounded-lg border border-transparent",
                      "data-[active=true]:border-primary/20 data-[active=true]:bg-gradient-to-r data-[active=true]:from-primary/10 data-[active=true]:to-primary/5"
                    )}
                  >
                    {/* Active indicator */}
                    {activeTab === item.value && (
                      <div className="absolute left-0 top-1/2 h-4 w-0.5 -translate-y-1/2 rounded-full bg-primary transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)] animate-in slide-in-from-left-2" />
                    )}

                    <div className={cn(
                      "flex h-5 w-5 items-center justify-center rounded-md shrink-0",
                      "transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]",
                      "group-hover:scale-105",
                      activeTab === item.value
                        ? "bg-primary/20 text-primary shadow-sm"
                        : "bg-muted/50 text-muted-foreground group-hover:bg-accent group-hover:text-accent-foreground"
                    )}>
                      <item.icon className={cn(
                        "h-3 w-3 transition-transform duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]",
                        activeTab === item.value && "scale-110"
                      )} />
                    </div>

                    <div className="flex-1 text-left min-w-0">
                      <div className="flex items-center justify-between">
                        <span className="font-medium truncate">{item.title}</span>
                        {item.badge && (
                          <Badge
                            variant="secondary"
                            className={cn(
                              "h-4 px-1.5 text-[9px] font-medium shrink-0 ml-1",
                              item.badge === "AI" && "bg-gradient-to-r from-purple-500 to-blue-500 text-white border-0"
                            )}
                          >
                            {item.badge === "AI" && <Sparkles className="h-2 w-2 mr-0.5" />}
                            {item.badge}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

      </SidebarContent>
    </Sidebar>
  )
}
