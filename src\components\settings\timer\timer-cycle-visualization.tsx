'use client';

import React from 'react';
import { <PERSON>, <PERSON>, Moon } from 'lucide-react';
import { motion } from 'framer-motion';

interface TimerCycleVisualizationProps {
  pomodoroMinutes: number;
  shortBreakMinutes: number;
  longBreakMinutes: number;
  sessionsCount: number;
}

export const TimerCycleVisualization = React.memo(({
  pomodoroMinutes,
  shortBreakMinutes,
  longBreakMinutes,
  sessionsCount
}: TimerCycleVisualizationProps) => {
  const MotionDiv = motion.div;

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const item = {
    hidden: { scale: 0.9, opacity: 0 },
    show: { scale: 1, opacity: 1 }
  };

  return (
    <MotionDiv
      className="flex flex-wrap items-center gap-1.5 overflow-x-auto overflow-y-auto scrollbar-hide py-1 pb-2"
      variants={container}
      initial="hidden"
      animate="show"
      style={{ maxWidth: '100%' }}
    >
      {Array.from({ length: sessionsCount }).map((_, i) => (
        <React.Fragment key={`cycle-${i}`}>
          {i > 0 && (
            <MotionDiv
              className="h-0.5 w-3 bg-gray-200 dark:bg-gray-700 mx-0.5 rounded-full"
              variants={item}
            />
          )}

          <MotionDiv
            className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs flex items-center gap-1 shadow-sm"
            variants={item}
          >
            <Clock className="h-3 w-3" />
            <span>{pomodoroMinutes}m</span>
          </MotionDiv>

          {i < sessionsCount - 1 && (
            <>
              <MotionDiv
                className="h-0.5 w-3 bg-gray-200 dark:bg-gray-700 mx-0.5 rounded-full"
                variants={item}
              />

              <MotionDiv
                className="px-2 py-1 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 rounded-full text-xs flex items-center gap-1 shadow-sm"
                variants={item}
              >
                <Coffee className="h-3 w-3" />
                <span>{shortBreakMinutes}m</span>
              </MotionDiv>
            </>
          )}
        </React.Fragment>
      ))}

      <MotionDiv
        className="h-0.5 w-3 bg-gray-200 dark:bg-gray-700 mx-0.5 rounded-full"
        variants={item}
      />

      <MotionDiv
        className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-xs flex items-center gap-1 shadow-sm"
        variants={item}
      >
        <Moon className="h-3 w-3" />
        <span>{longBreakMinutes}m</span>
      </MotionDiv>
    </MotionDiv>
  );
});

TimerCycleVisualization.displayName = 'TimerCycleVisualization';