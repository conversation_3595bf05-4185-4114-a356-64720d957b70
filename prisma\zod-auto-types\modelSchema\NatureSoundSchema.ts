import { z } from 'zod';
import { NatureSoundCategorySchema } from '../inputTypeSchemas/NatureSoundCategorySchema'
import { MediaSourceSchema } from '../inputTypeSchemas/MediaSourceSchema'
import { UserRoleSchema } from '../inputTypeSchemas/UserRoleSchema'
import { UserWithRelationsSchema, UserPartialWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations } from './UserSchema'
import { NaturePlaylistWithRelationsSchema, NaturePlaylistPartialWithRelationsSchema } from './NaturePlaylistSchema'
import type { NaturePlaylistWithRelations, NaturePlaylistPartialWithRelations } from './NaturePlaylistSchema'
import { MusicPlaylistUserWithRelationsSchema, MusicPlaylistUserPartialWithRelationsSchema } from './MusicPlaylistUserSchema'
import type { MusicPlaylistUserWithRelations, MusicPlaylistUserPartialWithRelations } from './MusicPlaylistUserSchema'

/////////////////////////////////////////
// NATURE SOUND SCHEMA
/////////////////////////////////////////

export const NatureSoundSchema = z.object({
  category: NatureSoundCategorySchema.array(),
  source: MediaSourceSchema.nullish(),
  creatorType: UserRoleSchema,
  id: z.string().cuid(),
  title: z.string().min(1),
  src: z.string().url().nullish(),
  isPublic: z.boolean(),
  userId: z.string(),
  createdAt: z.union([z.date(), z.string().datetime()]),
  updatedAt: z.union([z.date(), z.string().datetime()]),
})

export type NatureSound = z.infer<typeof NatureSoundSchema>

/////////////////////////////////////////
// NATURE SOUND PARTIAL SCHEMA
/////////////////////////////////////////

export const NatureSoundPartialSchema = NatureSoundSchema.partial()

export type NatureSoundPartial = z.infer<typeof NatureSoundPartialSchema>

/////////////////////////////////////////
// NATURE SOUND RELATION SCHEMA
/////////////////////////////////////////

export type NatureSoundRelations = {
  user: UserWithRelations;
  naturePlaylists: NaturePlaylistWithRelations[];
  musicPlaylistsUser: MusicPlaylistUserWithRelations[];
};

export type NatureSoundWithRelations = z.infer<typeof NatureSoundSchema> & NatureSoundRelations

export const NatureSoundWithRelationsSchema: z.ZodType<NatureSoundWithRelations> = NatureSoundSchema.merge(z.object({
  user: z.lazy(() => UserWithRelationsSchema),
  naturePlaylists: z.lazy(() => NaturePlaylistWithRelationsSchema).array(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// NATURE SOUND PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type NatureSoundPartialRelations = {
  user?: UserPartialWithRelations;
  naturePlaylists?: NaturePlaylistPartialWithRelations[];
  musicPlaylistsUser?: MusicPlaylistUserPartialWithRelations[];
};

export type NatureSoundPartialWithRelations = z.infer<typeof NatureSoundPartialSchema> & NatureSoundPartialRelations

export const NatureSoundPartialWithRelationsSchema: z.ZodType<NatureSoundPartialWithRelations> = NatureSoundPartialSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  naturePlaylists: z.lazy(() => NaturePlaylistPartialWithRelationsSchema).array(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserPartialWithRelationsSchema).array(),
})).partial()

export type NatureSoundWithPartialRelations = z.infer<typeof NatureSoundSchema> & NatureSoundPartialRelations

export const NatureSoundWithPartialRelationsSchema: z.ZodType<NatureSoundWithPartialRelations> = NatureSoundSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  naturePlaylists: z.lazy(() => NaturePlaylistPartialWithRelationsSchema).array(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserPartialWithRelationsSchema).array(),
}).partial())

export default NatureSoundSchema;
