"use client"

import { useState, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Search,
  Plus,
  Music2,
  Star,
  Filter,
  X,
  Play,
  Pause,
  Volume2,
  VolumeX
} from "lucide-react"
import { useGetMusics } from "@schemas/Music/music-query"
import { useAddMusicToMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { useAudioStore } from "@/lib/audio-store"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import { AddToPlaylistDialog } from "../../_components/add-to-playlist-dialog"

interface Music {
  id: string
  title: string
  src: string | null
  genres: string[]
  isPublic: boolean
  rating?: number | null
}

export function MusicsRoute() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedGenres, setSelectedGenres] = useState<string[]>([])
  const [selectedMusicId, setSelectedMusicId] = useState<string | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  const { data: musics, isLoading } = useGetMusics({ isPublic: true })
  const addMusicToPlaylist = useAddMusicToMusicPlaylistUser()

  // Global audio player state
  const {
    globalPlayer,
    setGlobalPlayerTrack,
    setGlobalPlayerPlaying,
    stopGlobalPlayer
  } = useAudioStore()

  // Get unique genres for filtering
  const availableGenres = useMemo(() => {
    if (!musics) return []
    const genreSet = new Set<string>()
    musics.forEach(music => {
      music.genres?.forEach(genre => genreSet.add(genre))
    })
    return Array.from(genreSet).sort()
  }, [musics])

  // Filter musics based on search and genre filters
  const filteredMusics = useMemo(() => {
    if (!musics) return []
    
    return musics.filter(music => {
      const matchesSearch = music.title.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesGenre = selectedGenres.length === 0 || 
        music.genres?.some(genre => selectedGenres.includes(genre))
      
      return matchesSearch && matchesGenre
    })
  }, [musics, searchQuery, selectedGenres])

  const handleGenreToggle = (genre: string) => {
    setSelectedGenres(prev => 
      prev.includes(genre) 
        ? prev.filter(g => g !== genre)
        : [...prev, genre]
    )
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedGenres([])
  }

  const handleAddToPlaylist = (musicId: string) => {
    setSelectedMusicId(musicId)
    setIsAddDialogOpen(true)
  }

  const handleAddMusicToPlaylist = async (playlistId: string) => {
    if (!selectedMusicId) return

    await addMusicToPlaylist.mutateAsync({
      musicPlaylistUserId: playlistId,
      musicIds: [selectedMusicId]
    })
  }

  const handlePlayMusic = (music: Music) => {
    const track = {
      id: music.id,
      title: music.title,
      src: music.src || `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`,
      type: 'music' as const,
      genres: music.genres
    }

    if (globalPlayer.currentTrack?.id === music.id) {
      // If already playing this track, toggle play/pause
      setGlobalPlayerPlaying(!globalPlayer.isPlaying)
    } else {
      // Play new track
      setGlobalPlayerTrack(track)
    }
  }

  const selectedMusic = selectedMusicId ? musics?.find(m => m.id === selectedMusicId) : null

  return (
    <>
      <div className="space-y-6 pb-32">
      {/* Header */}
      <div className="space-y-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Musics</h1>
          <p className="text-muted-foreground">
            Discover and add music tracks to enhance your focus sessions
          </p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search music tracks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {(searchQuery || selectedGenres.length > 0) && (
            <Button
              variant="outline"
              onClick={clearFilters}
              className="gap-2 shrink-0"
            >
              <X className="h-4 w-4" />
              Clear Filters
            </Button>
          )}
        </div>

        {/* Genre Filters */}
        {availableGenres.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Genres</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {availableGenres.map(genre => (
                <Badge
                  key={genre}
                  variant={selectedGenres.includes(genre) ? "default" : "outline"}
                  className={cn(
                    "cursor-pointer transition-all duration-200 hover:scale-105",
                    selectedGenres.includes(genre) && "bg-primary text-primary-foreground"
                  )}
                  onClick={() => handleGenreToggle(genre)}
                >
                  {genre}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Music List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="grid gap-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: i * 0.05 }}
                className="flex items-center gap-4 p-4 rounded-lg border border-border/50 bg-muted/10"
              >
                <Skeleton className="h-12 w-12 rounded-lg" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <div className="flex gap-2">
                    <Skeleton className="h-3 w-16" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                </div>
                <Skeleton className="h-8 w-20" />
              </motion.div>
            ))}
          </div>
        ) : filteredMusics.length === 0 ? (
          <div className="text-center py-12">
            <div className="p-4 rounded-full bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 w-fit mx-auto mb-4">
              <Music2 className="h-8 w-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h3 className="font-semibold text-lg mb-2">No music tracks found</h3>
            <p className="text-muted-foreground">
              {searchQuery || selectedGenres.length > 0
                ? "Try adjusting your search or filters"
                : "No music tracks are available at the moment"
              }
            </p>
          </div>
        ) : (
          <div className="grid gap-3">
            <AnimatePresence>
              {filteredMusics.map((music, index) => {
                const isCurrentlyPlaying = globalPlayer.currentTrack?.id === music.id && globalPlayer.isPlaying
                const isCurrentTrack = globalPlayer.currentTrack?.id === music.id

                return (
                  <motion.div
                    key={music.id}
                    layout
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    transition={{ duration: 0.2, delay: index * 0.02 }}
                    className={cn(
                      "group flex items-center gap-4 p-4 rounded-lg border transition-all duration-200",
                      "border-border/30 hover:border-border hover:bg-muted/20",
                      isCurrentTrack && "bg-orange-50/50 dark:bg-orange-950/20 border-orange-200 dark:border-orange-800"
                    )}
                  >
                    {/* Music Icon / Play Button */}
                    <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 flex items-center justify-center shrink-0 relative">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation()
                          handlePlayMusic(music)
                        }}
                        className="w-12 h-12 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-800 absolute inset-0"
                      >
                        {isCurrentlyPlaying ? (
                          <Pause className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                        ) : (
                          <Play className="h-5 w-5 text-orange-600 dark:text-orange-400 ml-0.5" />
                        )}
                      </Button>
                    </div>

                  {/* Music Info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium line-clamp-1 mb-1">{music.title}</h3>
                    <div className="flex items-center gap-2 flex-wrap">
                      {music.genres?.slice(0, 3).map(genre => (
                        <Badge key={genre} variant="secondary" className="text-xs">
                          {genre}
                        </Badge>
                      ))}
                      {music.rating && (
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <span className="text-xs text-muted-foreground">
                            {music.rating.toFixed(1)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Add Button */}
                  <Button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleAddToPlaylist(music.id)
                    }}
                    size="sm"
                    className={cn(
                      "h-8 px-3 text-xs transition-all duration-200 gap-2",
                      "bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white"
                    )}
                  >
                    <Plus className="h-3 w-3" />
                    Add to Playlist
                  </Button>
                  </motion.div>
                )
              })}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Add to Playlist Dialog */}
      <AddToPlaylistDialog
        isOpen={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        itemType="music"
        itemTitle={selectedMusic?.title || ""}
        onAddToPlaylist={handleAddMusicToPlaylist}
      />
      </div>
    </>
  )
}
