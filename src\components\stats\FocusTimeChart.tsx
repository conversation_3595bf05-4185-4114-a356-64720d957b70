"use client"

import { useRef } from "react"
import { useTheme } from "next-themes"
import { Line, LineChart, ResponsiveContainer, Tooltip, XAxis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

interface FocusTimeChartProps {
  data: Array<{
    date: string
    minutes: number
  }>
}

interface TooltipProps {
  active?: boolean
  payload?: Array<{ value: number }>
  label?: string
}

export function FocusTimeChart({ data }: FocusTimeChartProps) {
  const { theme } = useTheme()
  const tooltipRef = useRef<HTMLDivElement>(null)

  // Format date for x-axis
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.getDate().toString()
  }

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: TooltipProps) => {
    if (active && payload && payload.length) {
      const date = new Date(label || "")
      const formattedDate = date.toLocaleDateString("en-US", { month: "short", day: "numeric" })
      const minutes = payload[0].value

      return (
        <div ref={tooltipRef} className="rounded-md border border-border bg-card p-3 shadow-md">
          <p className="mb-1 font-medium">{formattedDate}</p>
          <p className="text-primary">
            <span className="font-bold">{minutes}</span> minutes
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 10, left: 0, bottom: 5 }}>
          <XAxis
            dataKey="date"
            tickFormatter={formatDate}
            stroke="currentColor"
            className="text-muted-foreground"
            tickLine={false}
            axisLine={false}
            tick={{ fontSize: 12 }}
            dy={10}
          />
          <YAxis
            stroke="currentColor"
            className="text-muted-foreground"
            tickLine={false}
            axisLine={false}
            tick={{ fontSize: 12 }}
            width={30}
          />
          <Tooltip content={<CustomTooltip />} />
          <Line
            type="monotone"
            dataKey="minutes"
            stroke="#5576F5"
            strokeWidth={2}
            connectNulls={true}
            dot={{ r: 4, fill: "#5576F5", stroke: "#ffffff", strokeWidth: 2 }}
            activeDot={{ r: 6, fill: "#5576F5", stroke: "#ffffff", strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
