'use client';

import { useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User, Save, TrendingUp, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { useUserStore } from '@/store/userStore';
import { cn } from '@/lib/utils';

interface SignInPromptProps {
  isVisible: boolean;
  onClose: () => void;
  onSignIn: () => void;
  className?: string;
}

export function SignInPrompt({
  isVisible,
  onClose,
  onSignIn,
  className
}: SignInPromptProps) {
  const { updatePreferences } = useUserStore();

  const handleDismiss = useCallback(() => {
    // Update preferences to remember dismissal
    try {
      updatePreferences({
        ui: {
          dismissedSignInPrompt: true,
        },
      });
    } catch (error) {
      console.error('Failed to update sign-in prompt dismissal preference:', error);
    }

    onClose();
  }, [updatePreferences, onClose]);

  const handleSignIn = useCallback(() => {
    onSignIn();
  }, [onSignIn]);

  // Auto-hide after 12 seconds if user doesn't interact
  useEffect(() => {
    if (!isVisible) return;

    const autoHideTimer = setTimeout(() => {
      handleDismiss();
    }, 12000);

    return () => clearTimeout(autoHideTimer);
  }, [isVisible, handleDismiss]);

  return (
    <Dialog open={isVisible} onOpenChange={(open) => !open && handleDismiss()}>
      <DialogContent className={cn("sm:max-w-md", className)}>
        <DialogHeader className="text-center sm:text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.1, duration: 0.3 }}
            >
              <Sparkles className="h-6 w-6 text-primary" />
            </motion.div>
          </div>
          <DialogTitle className="text-xl font-semibold">
            Great session!
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Save your progress and unlock powerful productivity features
          </DialogDescription>
        </DialogHeader>

        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.3 }}
        >
          {/* Benefits */}
          <div className="space-y-3">
            <div className="flex items-center gap-3 text-sm">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                <TrendingUp className="h-4 w-4 text-primary" />
              </div>
              <span className="text-foreground">Track your productivity streaks</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                <Save className="h-4 w-4 text-primary" />
              </div>
              <span className="text-foreground">Save your session history</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                <User className="h-4 w-4 text-primary" />
              </div>
              <span className="text-foreground">Sync across all your devices</span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col gap-2 pt-2">
            <Button
              onClick={handleSignIn}
              className="w-full"
              size="sm"
            >
              Sign In to Save Progress
            </Button>
            <Button
              onClick={handleDismiss}
              variant="ghost"
              size="sm"
              className="w-full text-muted-foreground hover:text-foreground"
            >
              Maybe later
            </Button>
          </div>

          {/* Progress indicator */}
          <div className="relative mt-4">
            <div className="h-1 w-full bg-muted rounded-full overflow-hidden">
              <motion.div
                initial={{ width: "100%" }}
                animate={{ width: "0%" }}
                transition={{ duration: 12, ease: "linear" }}
                className="h-full bg-primary/60 rounded-full"
              />
            </div>
            <p className="text-xs text-muted-foreground text-center mt-2">
              Auto-dismiss in 12 seconds
            </p>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
