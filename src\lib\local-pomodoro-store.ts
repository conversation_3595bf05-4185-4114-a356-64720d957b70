'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useUserStore } from '@/store/userStore';

// Local session structure matching the backend
export interface LocalPomodoroSession {
  id: string;
  startTime: string; // ISO string
  endTime: string | null; // ISO string
  totalDuration: number; // seconds
  focusDuration: number | null; // seconds
  breakDuration: number | null; // seconds
  intervalType: 'FOCUS' | 'SHORT_BREAK' | 'LONG_BREAK';
  completed: boolean;
  interrupted: boolean;
  note: string | null;
  interruptedSessions: Array<{
    startTime: string;
    endTime: string;
  }> | null;
  taskId: string | null;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
}

export interface LocalPomodoroStats {
  totalSessions: number;
  completedSessions: number;
  interruptedSessions: number;
  totalDuration: number;
  focusSessions: number;
  shortBreakSessions: number;
  longBreakSessions: number;
  focusDuration: number;
  shortBreakDuration: number;
  longBreakDuration: number;
  todayFocusTime: number; // minutes
  todayCompletedSessions: number;
  weekTotalDuration: number; // hours
  weekCompletionRate: number; // percentage
}

interface LocalPomodoroStore {
  sessions: LocalPomodoroSession[];
  
  // Actions
  addSession: (session: Omit<LocalPomodoroSession, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateSession: (id: string, updates: Partial<LocalPomodoroSession>) => void;
  deleteSession: (id: string) => void;
  clearAllSessions: () => void;
  
  // Stats calculations
  getQuickStats: () => {
    todayFocusTime: number;
    todayCompletedSessions: number;
    todaySessions: Array<{
      id: string;
      title: string;
      duration: number;
      completed: boolean;
      interrupted: boolean;
      timeRange: string;
      type: string;
      startTime: string;
      endTime: string;
    }>;
    weekTotalDuration: number;
    weekCompletionRate: number;
    dateRange: {
      today: string;
      weekStart: string;
    };
  };
  
  getComprehensiveStats: (days?: number) => LocalPomodoroStats;
  
  // Export for transfer
  exportSessions: () => LocalPomodoroSession[];
  
  // Debug functions
  getSessionCount: () => number;
  debugSessions: () => void;
  clearDuplicates: () => void;
}

// Helper functions
const generateId = () => `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const formatDateString = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const isToday = (dateString: string): boolean => {
  const today = formatDateString(new Date());
  const sessionDate = formatDateString(new Date(dateString));
  return today === sessionDate;
};

const isWithinDays = (dateString: string, days: number): boolean => {
  const sessionDate = new Date(dateString);
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  return sessionDate >= cutoffDate;
};

export const useLocalPomodoroStore = create<LocalPomodoroStore>()(
  persist(
    (set, get) => ({
      sessions: [],

      addSession: (sessionData) => {
        // Check if user is authenticated - if so, don't save to local storage
        const isAuthenticated = useUserStore.getState().isAuthenticated;
        if (isAuthenticated) {
          console.warn('User is authenticated - sessions should be saved to database, not local storage');
          return;
        }

        const now = new Date().toISOString();
        const newSession: LocalPomodoroSession = {
          ...sessionData,
          id: generateId(),
          createdAt: now,
          updatedAt: now,
        };

        console.log('Adding local session:', {
          id: newSession.id,
          startTime: newSession.startTime,
          endTime: newSession.endTime,
          intervalType: newSession.intervalType,
          totalDuration: newSession.totalDuration,
          currentSessionCount: get().sessions.length
        });

        set((state) => ({
          sessions: [...state.sessions, newSession]
        }));

        console.log('Local session added. Total sessions:', get().sessions.length);
      },

      updateSession: (id, updates) => {
        set((state) => ({
          sessions: state.sessions.map((session) =>
            session.id === id
              ? { ...session, ...updates, updatedAt: new Date().toISOString() }
              : session
          )
        }));
      },

      deleteSession: (id) => {
        set((state) => ({
          sessions: state.sessions.filter((session) => session.id !== id)
        }));
      },

      clearAllSessions: () => {
        set({ sessions: [] });
      },

      getQuickStats: () => {
        const sessions = get().sessions;
        const now = new Date();
        
        // Today's date range
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        // Week start (7 days ago)
        const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        weekStart.setDate(weekStart.getDate() - 7);

        // Filter today's sessions
        const todaySessions = sessions.filter(session => 
          isToday(session.startTime)
        );

        // Filter week's sessions
        const weekSessions = sessions.filter(session => 
          isWithinDays(session.startTime, 7)
        );

        // Calculate today's focus time (only focus sessions)
        const todayFocusSessions = todaySessions.filter(s => s.intervalType === 'FOCUS');
        const todayFocusTime = todayFocusSessions.reduce((sum, s) => {
          const duration = s.focusDuration || s.totalDuration;
          return sum + Math.round(duration / 60); // Convert to minutes
        }, 0);

        // Calculate today's completed focus sessions count
        const todayCompletedSessions = todayFocusSessions.filter(s => s.completed).length;

        // Calculate week totals
        const weekFocusSessions = weekSessions.filter(s => s.intervalType === 'FOCUS');
        const weekTotalSessions = weekFocusSessions.length;
        const weekCompletedSessions = weekFocusSessions.filter(s => s.completed).length;
        const weekTotalDuration = weekSessions.reduce((sum, s) => sum + s.totalDuration, 0);

        // Format today's sessions for display
        const formattedTodaySessions = todaySessions.map(session => {
          const startTime = new Date(session.startTime);
          const endTime = session.endTime ? new Date(session.endTime) : new Date(startTime.getTime() + session.totalDuration * 1000);

          // Use appropriate duration based on session type
          let activeDuration = session.totalDuration;
          if (session.intervalType === 'FOCUS' && session.focusDuration !== null) {
            activeDuration = session.focusDuration;
          } else if ((session.intervalType === 'SHORT_BREAK' || session.intervalType === 'LONG_BREAK') && session.breakDuration !== null) {
            activeDuration = session.breakDuration;
          }

          return {
            id: session.id,
            title: `${session.intervalType.charAt(0) + session.intervalType.slice(1).toLowerCase()} Session`,
            duration: Math.round(activeDuration / 60), // Convert to minutes
            completed: session.completed,
            interrupted: session.interrupted,
            timeRange: `${startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - ${endTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`,
            type: session.intervalType.toLowerCase(),
            startTime: startTime.toISOString(),
            endTime: endTime.toISOString(),
            taskId: session.taskId // Include taskId so we can populate title later
          };
        });

        return {
          todayFocusTime,
          todayCompletedSessions,
          todaySessions: formattedTodaySessions,
          weekTotalDuration: Math.round(weekTotalDuration / 3600), // Convert to hours
          weekCompletionRate: weekTotalSessions > 0 ? Math.round((weekCompletedSessions / weekTotalSessions) * 100) : 0,
          dateRange: {
            today: formatDateString(today),
            weekStart: formatDateString(weekStart)
          }
        };
      },

      getComprehensiveStats: (days = 7) => {
        const sessions = get().sessions.filter(session => 
          isWithinDays(session.startTime, days)
        );

        // Basic counts
        const totalSessions = sessions.length;
        const completedSessions = sessions.filter(s => s.completed && s.intervalType === 'FOCUS').length;
        const interruptedSessions = sessions.filter(s => s.interrupted && s.intervalType === 'FOCUS').length;
        const totalDuration = sessions.reduce((sum, s) => sum + s.totalDuration, 0);

        // Session type breakdowns
        const focusSessions = sessions.filter(s => s.intervalType === 'FOCUS').length;
        const shortBreakSessions = sessions.filter(s => s.intervalType === 'SHORT_BREAK').length;
        const longBreakSessions = sessions.filter(s => s.intervalType === 'LONG_BREAK').length;

        // Duration by session type
        const focusDuration = sessions
          .filter(s => s.intervalType === 'FOCUS')
          .reduce((sum, s) => sum + (s.focusDuration || s.totalDuration), 0);

        const shortBreakDuration = sessions
          .filter(s => s.intervalType === 'SHORT_BREAK')
          .reduce((sum, s) => sum + (s.breakDuration || s.totalDuration), 0);

        const longBreakDuration = sessions
          .filter(s => s.intervalType === 'LONG_BREAK')
          .reduce((sum, s) => sum + (s.breakDuration || s.totalDuration), 0);

        // Today's metrics
        const todaySessions = sessions.filter(session => isToday(session.startTime));
        const todayFocusSessions = todaySessions.filter(s => s.intervalType === 'FOCUS');
        const todayFocusTime = todayFocusSessions.reduce((sum, s) => {
          const duration = s.focusDuration || s.totalDuration;
          return sum + Math.round(duration / 60);
        }, 0);
        const todayCompletedSessions = todayFocusSessions.filter(s => s.completed).length;

        // Week totals
        const weekFocusSessions = sessions.filter(s => s.intervalType === 'FOCUS');
        const weekTotalSessions = weekFocusSessions.length;
        const weekCompletedSessions = weekFocusSessions.filter(s => s.completed).length;
        const weekTotalDuration = sessions.reduce((sum, s) => sum + s.totalDuration, 0);

        return {
          totalSessions,
          completedSessions,
          interruptedSessions,
          totalDuration,
          focusSessions,
          shortBreakSessions,
          longBreakSessions,
          focusDuration,
          shortBreakDuration,
          longBreakDuration,
          todayFocusTime,
          todayCompletedSessions,
          weekTotalDuration: Math.round(weekTotalDuration / 3600),
          weekCompletionRate: weekTotalSessions > 0 ? Math.round((weekCompletedSessions / weekTotalSessions) * 100) : 0,
        };
      },

      exportSessions: () => {
        return get().sessions;
      },

      getSessionCount: () => {
        return get().sessions.length;
      },

      debugSessions: () => {
        console.log('Current sessions:', get().sessions);
      },

      clearDuplicates: () => {
        set((state) => ({
          sessions: state.sessions.filter((session, index, self) =>
            self.findIndex(s => s.id === session.id) === index
          )
        }));
      },
    }),
    {
      name: 'local-pomodoro-sessions',
      // Only persist sessions data
      partialize: (state) => ({ sessions: state.sessions }),
    }
  )
); 