'use client';

import { useState, useEffect, useCallback } from 'react';
import { Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PomodoroTimerProps {
  initialFocusMinutes?: number;
  className?: string;
}

export function PomodoroTimer({
  initialFocusMinutes = 25,
  className,
}: PomodoroTimerProps) {
  const [timeLeft, setTimeLeft] = useState(initialFocusMinutes * 60);

  // Format time as MM:SS
  const formatTime = useCallback((seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Timer countdown effect - auto start
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          // Loop back to initial time when it reaches zero
          return initialFocusMinutes * 60;
        }
        return prevTime - 1;
      });
    }, 1000);
    
    return () => clearInterval(interval);
  }, [initialFocusMinutes]);

  return (
    <div 
      className={cn(
        'flex items-center px-3 py-1.5 rounded-full',
        'border border-white/10 backdrop-blur-sm',
        'bg-black/20 w-[90px]', // Fixed width to prevent flickering
        className
      )}
    >
      <Clock className="h-3.5 w-3.5 text-white/80 mr-2 flex-shrink-0" />
      <div className="font-medium text-white/90 text-xs tabular-nums w-full text-center">
        {formatTime(timeLeft)}
      </div>
    </div>
  );
} 