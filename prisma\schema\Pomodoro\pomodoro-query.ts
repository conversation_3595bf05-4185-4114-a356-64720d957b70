import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import client from "@/lib/trpc";
import { toast } from "sonner";

// Get all pomodoro sessions
export type GetPomodoroSessions_ResponseType = InferResponseType<
  (typeof client.api.pomodoro)["$get"],
  200
>;

export type GetPomodoroSessions_ResponseTypeSuccess = Extract<
  GetPomodoroSessions_ResponseType,
  { data: object }
>["data"];

export const useGetPomodoroSessions = (filters?: {
  taskId?: string;
  completed?: boolean;
  intervalType?: "FOCUS" | "SHORT_BREAK" | "LONG_BREAK";
}) => {
  return useQuery({
    queryKey: ["pomodoro", filters],
    queryFn: async () => {
      const queryParams = new URLSearchParams();

      if (filters?.taskId) {
        queryParams.append("taskId", filters.taskId);
      }

      if (filters?.completed !== undefined) {
        queryParams.append("completed", String(filters.completed));
      }

      if (filters?.intervalType) {
        queryParams.append("intervalType", filters.intervalType);
      }

      const queryString = queryParams.toString();

      let response;
      if (queryString) {
        response = await client.api.pomodoro.$get({
          query: {
            taskId: filters?.taskId,
            completed: filters?.completed !== undefined ? String(filters.completed) : undefined,
            intervalType: filters?.intervalType
          }
        });
      } else {
        response = await client.api.pomodoro.$get();
      }

      if (!response.ok) {
        throw new Error("Failed to fetch pomodoro sessions");
      }
      const { data } = await response.json();
      return data;
    },
  });
};

// Get single pomodoro session
type GetPomodoroSession_ResponseType = InferResponseType<
  (typeof client.api.pomodoro)[":id"]["$get"],
  200
>;

export type GetPomodoroSession_ResponseTypeSuccess = Extract<
  GetPomodoroSession_ResponseType,
  { data: object }
>["data"];

export const useGetPomodoroSession = (id?: string) => {
  return useQuery({
    queryKey: ["pomodoro", { id }],
    queryFn: async () => {
      if (!id) throw new Error("No pomodoro session ID provided");

      const response = await client.api.pomodoro[":id"]["$get"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch pomodoro session");
      }

      const { data } = await response.json();
      return data;
    },
    enabled: !!id,
  });
};

// Create pomodoro session
interface CreatePomodoroSessionSuccessResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

type CreatePomodoroSessionRequest = InferRequestType<
  (typeof client.api.pomodoro)["$post"]
>;

// Simple tracker for most recent session creation (no need for a complex set)
let lastSessionCreation: string | null = null;

export const useCreatePomodoroSession = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation<
    CreatePomodoroSessionSuccessResponse,
    Error,
    CreatePomodoroSessionRequest
  >({
    mutationFn: async ({ form }) => {
      // Create a simple session key based on start time and end time
      const sessionKey = `${form.startTime}-${form.endTime}`;

      // If this is the same as the last request we processed, skip it
      if (sessionKey === lastSessionCreation) {
        console.log('Skipping duplicate session creation request', sessionKey);
        throw new Error('Duplicate session request');
      }

      // Update the last session key
      lastSessionCreation = sessionKey;

      console.log('Creating pomodoro session:', sessionKey);

      const response = await client.api.pomodoro.$post({ form });

      if (!response.ok) {
        throw new Error("Failed to create pomodoro session");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      // toast.success("Pomodoro session created successfully");
      
      // More specific invalidation to ensure consistency
      queryClient.invalidateQueries({ queryKey: ["pomodoro"] });
      queryClient.invalidateQueries({ queryKey: ["pomodoro", "quick-stats"] });
      queryClient.invalidateQueries({ queryKey: ["pomodoro", "stats"] });

      // Invalidate task-related queries with specific task ID first, then general
      if (data.taskId) {
        queryClient.invalidateQueries({ queryKey: ["tasks", { id: data.taskId }] });
        queryClient.invalidateQueries({ queryKey: ["tasks"] });
      } else {
        // If no taskId, still invalidate all tasks to be safe
        queryClient.invalidateQueries({ queryKey: ["tasks"] });
      }
      
      // Clear the duplicate prevention key after successful creation
      setTimeout(() => {
        lastSessionCreation = null;
      }, 1000);
    },
    onError: (error) => {
      // Clear the duplicate prevention key on error (except for intended duplicates)
      if (error.message !== 'Duplicate session request') {
        lastSessionCreation = null;
        toast.error(`Failed to create pomodoro session: ${error.message}`);
      }
    },
  });

  return mutation;
};

// Update pomodoro session
type UpdatePomodoroSession_ResponseType = InferResponseType<
  (typeof client.api.pomodoro)[":id"]["$patch"],
  200
>;

export type UpdatePomodoroSession_ResponseTypeSuccess = Extract<
  UpdatePomodoroSession_ResponseType,
  { data: object }
>["data"];

type UpdatePomodoroSessionRequest = InferRequestType<
  (typeof client.api.pomodoro)[":id"]["$patch"]
>;

export const useUpdatePomodoroSession = () => {
  const queryClient = useQueryClient();

  return useMutation<
    UpdatePomodoroSession_ResponseType,
    Error,
    UpdatePomodoroSessionRequest
  >({
    mutationFn: async (variables) => {
      const { form, param } = variables;

      if (!param?.id) {
        throw new Error("No pomodoro session ID provided");
      }

      const response = await client.api.pomodoro[":id"]["$patch"]({
        form,
        param: { id: param.id },
      });

      if (!response.ok) {
        throw new Error(`Failed to update pomodoro session. Status: ${response.status}`);
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Pomodoro session updated successfully");
      
      // Invalidate specific session first
      const sessionId = data?.id;
      if (sessionId) {
        queryClient.invalidateQueries({ queryKey: ["pomodoro", { id: sessionId }] });
      }
      
      // Then invalidate broader queries
      queryClient.invalidateQueries({ queryKey: ["pomodoro"] });
      queryClient.invalidateQueries({ queryKey: ["pomodoro", "quick-stats"] });
      queryClient.invalidateQueries({ queryKey: ["pomodoro", "stats"] });

      // Invalidate task-related queries
      if (data.taskId) {
        queryClient.invalidateQueries({ queryKey: ["tasks", { id: data.taskId }] });
        queryClient.invalidateQueries({ queryKey: ["tasks"] });
      } else {
        // If no taskId, still invalidate all tasks to be safe
        queryClient.invalidateQueries({ queryKey: ["tasks"] });
      }
    },
    onError: (error) => {
      toast.error(`Failed to update pomodoro session: ${error.message}`);
    },
  });
};

// Delete pomodoro session
type DeletePomodoroSession_ResponseType = InferResponseType<
  (typeof client.api.pomodoro)[":id"]["$delete"],
  200
>;

export const useDeletePomodoroSession = () => {
  const queryClient = useQueryClient();

  return useMutation<DeletePomodoroSession_ResponseType, Error, { id: string; taskId?: string }>({
    mutationFn: async ({ id }) => {
      if (!id) throw new Error("No pomodoro session ID provided");

      const response = await client.api.pomodoro[":id"]["$delete"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to delete pomodoro session");
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Pomodoro session deleted successfully");
      
      // Invalidate pomodoro queries
      queryClient.invalidateQueries({ queryKey: ["pomodoro"] });
      queryClient.invalidateQueries({ queryKey: ["pomodoro", "quick-stats"] });
      queryClient.invalidateQueries({ queryKey: ["pomodoro", "stats"] });

      // Invalidate task-related queries
      if (variables.taskId) {
        queryClient.invalidateQueries({ queryKey: ["tasks", { id: variables.taskId }] });
        queryClient.invalidateQueries({ queryKey: ["tasks"] });
      } else {
        // If no taskId, still invalidate all tasks to be safe
        queryClient.invalidateQueries({ queryKey: ["tasks"] });
      }
    },
    onError: (error) => {
      toast.error(`Failed to delete pomodoro session: ${error.message}`);
    },
  });
};

// Get quick stats for popover
type GetPomodoroQuickStats_ResponseType = InferResponseType<
  (typeof client.api.pomodoro)["quick-stats"]["$get"],
  200
>;

export type GetPomodoroQuickStats_ResponseTypeSuccess = Extract<
  GetPomodoroQuickStats_ResponseType,
  { data: object }
>["data"];

// Quick stats response interface (lightweight for popover)
export interface PomodoroQuickStatsResponse {
  todayFocusTime: number; // in minutes
  todayCompletedSessions: number;
  todaySessions: Array<{
    id: string;
    title: string;
    duration: number; // in minutes
    completed: boolean;
    interrupted: boolean;
    timeRange: string;
    type: string;
    startTime: string;
    endTime: string;
  }>;
  weekTotalDuration: number; // in minutes (changed from hours for consistency)
  weekCompletionRate: number; // percentage
  dateRange: {
    today: string;
    weekStart: string;
  };
}

export const useGetPomodoroQuickStats = (enabled: boolean = true) => {
  return useQuery<PomodoroQuickStatsResponse>({
    queryKey: ["pomodoro", "quick-stats"],
    queryFn: async () => {
      const response = await client.api.pomodoro["quick-stats"].$get();

      if (!response.ok) {
        throw new Error("Failed to fetch quick pomodoro statistics");
      }

      const { data } = await response.json();
      return data;
    },
    enabled, // Only run query when enabled is true
    staleTime: 1000 * 60 * 2, // 2 minutes - quick stats can be slightly stale
    gcTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Get comprehensive pomodoro statistics for dashboard
type GetPomodoroStats_ResponseType = InferResponseType<
  (typeof client.api.pomodoro)["stats"]["$get"],
  200
>;

export type GetPomodoroStats_ResponseTypeSuccess = Extract<
  GetPomodoroStats_ResponseType,
  { data: object }
>["data"];

// The type definition for pomodoro stats response
export interface PomodoroStatsResponse {
  totalSessions: number;
  completedSessions: number;
  interruptedSessions: number;
  totalDuration: number; // Total elapsed time including interruptions
  completionRate: number;
  // Session type breakdown
  focusSessions: number;
  shortBreakSessions: number;
  longBreakSessions: number;
  // Duration by session type (active time excluding interruptions)
  focusDuration: number; // Actual focus time excluding interruptions
  shortBreakDuration: number; // Actual short break time excluding interruptions
  longBreakDuration: number; // Actual long break time excluding interruptions
  // Daily stats
  sessionsByDay: { [date: string]: { count: number; focusMinutes: number } }; // focusMinutes excludes interruption time
  // Advanced stats
  todaySessions: Array<{
    id: string;
    title: string;
    duration: number; // Active duration excluding interruptions
    completed: boolean;
    interrupted: boolean;
    timeRange: string;
    type: string;
    startTime: string;
    endTime: string;
  }>;
  streakData: Array<{ date: string; count: number }>;
  hourlyDistribution: Array<{ hour: number; value: number }>;
  weeklyComparison: Array<{ name: string; thisWeek: number; lastWeek: number }>; // Minutes exclude interruption time
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export interface DateRangeParams {
  startDate?: string;
  endDate?: string;
}

export const useGetPomodoroStats = (
  days?: number,
  specificDate?: string,
  dateRange?: DateRangeParams
) => {
  return useQuery<PomodoroStatsResponse>({
    queryKey: ["pomodoro", "stats", { days, specificDate, dateRange }],
    queryFn: async () => {
      const queryParams = new URLSearchParams();

      if (days !== undefined && !specificDate && !dateRange) {
        queryParams.append("days", String(days));
      }

      if (specificDate) {
        queryParams.append("date", specificDate);
      }

      if (dateRange?.startDate && dateRange?.endDate) {
        queryParams.append("startDate", dateRange.startDate);
        queryParams.append("endDate", dateRange.endDate);
      }

      const response = await client.api.pomodoro.stats.$get({
        query: {
          days: days !== undefined && !specificDate && !dateRange ? String(days) : undefined,
          date: specificDate,
          startDate: dateRange?.startDate,
          endDate: dateRange?.endDate
        }
      });

      if (!response.ok) {
        throw new Error("Failed to fetch pomodoro statistics");
      }

      const { data } = await response.json();
      return data;
    },
  });
};

// Add interface for bulk transfer
export interface BulkTransferSession {
  startTime: string;
  endTime: string | null;
  totalDuration: string;
  focusDuration: string | undefined;
  breakDuration: string | undefined;
  intervalType: 'FOCUS' | 'SHORT_BREAK' | 'LONG_BREAK';
  completed: string;
  interrupted: string;
  note?: string;
  interruptedSessions?: string;
  localTaskId?: string;
}

// Bulk transfer local sessions to database
type BulkTransferSessionsRequest = InferRequestType<
  (typeof client.api.pomodoro)["bulk-transfer"]["$post"]
>;

export const useBulkTransferSessions = () => {
  const queryClient = useQueryClient();

  return useMutation<
    { data: { transferred: number; skipped: number } },
    Error,
    { sessions: BulkTransferSession[] }
  >({
    mutationFn: async ({ sessions }) => {
      console.log('Transferring sessions to database:', sessions.length);

      const response = await client.api.pomodoro["bulk-transfer"].$post({ 
        json: { sessions } 
      });

      if (!response.ok) {
        throw new Error("Failed to transfer local sessions to database");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      // toast.success(`Successfully transferred ${data.transferred} sessions to your account`, {
      //   description: data.skipped > 0 ? `${data.skipped} sessions were skipped (too short)` : undefined,
      //   duration: 5000,
      // });
      
      // Invalidate all pomodoro queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["pomodoro"] });
      queryClient.invalidateQueries({ queryKey: ["pomodoro", "quick-stats"] });
      queryClient.invalidateQueries({ queryKey: ["pomodoro", "stats"] });
    },
    onError: (error) => {
      toast.error(`Failed to transfer sessions: ${error.message}`);
    },
  });
};