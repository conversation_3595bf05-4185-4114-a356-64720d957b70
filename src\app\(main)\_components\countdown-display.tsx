'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface CountdownDisplayProps {
  initialMinutes?: number;
  className?: string;
  onComplete?: () => void;
}

export const CountdownDisplay = ({
  initialMinutes = 25,
  className = "",
  onComplete
}: CountdownDisplayProps) => {
  const [timeLeft, setTimeLeft] = useState({
    minutes: initialMinutes,
    seconds: 0
  });
  const [isMinuteFlipping, setIsMinuteFlipping] = useState(false);
  const [isSecondFlipping, setIsSecondFlipping] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Actual countdown functionality that starts automatically
  useEffect(() => {
    // Start the countdown timer
    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        // If seconds is 0, decrement minutes and set seconds to 59
        if (prev.seconds === 0) {
          // If minutes is also 0, reset to initial time and trigger onComplete callback
          if (prev.minutes === 0) {
            onComplete?.();
            return {
              minutes: initialMinutes,
              seconds: 0
            };
          }
          // Otherwise decrement minutes and set seconds to 59
          setIsMinuteFlipping(true);
          setTimeout(() => setIsMinuteFlipping(false), 300);
          return {
            minutes: prev.minutes - 1,
            seconds: 59
          };
        }
        // Otherwise just decrement seconds
        return {
          ...prev,
          seconds: prev.seconds - 1
        };
      });

      // Add flipping animation effect when seconds change
      setIsSecondFlipping(true);
      setTimeout(() => setIsSecondFlipping(false), 300);
    }, 1000);

    // Cleanup interval on component unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [initialMinutes, onComplete]);

  // Update minutes when prop changes
  useEffect(() => {
    setTimeLeft({
      minutes: initialMinutes,
      seconds: 0
    });
  }, [initialMinutes]);

  const formatDigit = (value: number): string => {
    return String(value).padStart(2, '0');
  };

  return (
    <div className={cn(
      "flex items-center justify-center space-x-2 px-4 py-3 rounded-lg",
      "bg-gradient-to-br from-rose-50/90 to-white/95 dark:from-slate-800 dark:to-slate-900/90",
      "shadow-md shadow-rose-100/20 dark:shadow-slate-900/20",
      "border-2 border-rose-100/60 dark:border-slate-700/60",
      "transition-all duration-300 ease-in-out",
      className
    )}>
      {/* Minutes */}
      <div className="flex flex-col items-center">
        <div
          className={cn(
            "relative overflow-hidden transition-all duration-300 ease-in-out",
            "rounded-md",
            isMinuteFlipping ? "transform -translate-y-1" : ""
          )}
        >
          <span
            suppressHydrationWarning
            className="text-3xl md:text-4xl font-bold text-red-500 dark:text-red-400 tabular-nums"
          >
            {formatDigit(timeLeft.minutes)}
          </span>
        </div>
        <span className="text-[10px] uppercase text-slate-500 dark:text-slate-400 tracking-wider font-medium mt-0.5">
          min
        </span>
      </div>

      {/* Separator */}
      <div className="flex flex-col items-center justify-center">
        <span className="text-2xl md:text-3xl font-medium text-slate-400 dark:text-slate-500 animate-pulse">:</span>
        <div className="h-5"></div> {/* Spacer to align with the labels */}
      </div>

      {/* Seconds */}
      <div className="flex flex-col items-center">
        <div
          className={cn(
            "relative overflow-hidden transition-all duration-300 ease-in-out",
            "rounded-md",
            isSecondFlipping ? "transform -translate-y-1" : ""
          )}
        >
          <span
            suppressHydrationWarning
            className="text-3xl md:text-4xl font-bold text-slate-700 dark:text-slate-300 tabular-nums"
          >
            {formatDigit(timeLeft.seconds)}
          </span>
        </div>
        <span className="text-[10px] uppercase text-slate-500 dark:text-slate-400 tracking-wider font-medium mt-0.5">
          sec
        </span>
      </div>
    </div>
  );
};