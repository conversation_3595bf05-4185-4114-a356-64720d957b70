"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { FocusTimeChart } from "@/components/stats/FocusTimeChart"
import { CompletionRateChart } from "@/components/stats/CompletionRateChart"
import { WeeklyComparison } from "@/components/stats/WeeklyComparison"
import { FocusDistribution } from "@/components/stats/FocusDistribution"
import { DailyHeatmap } from "@/components/stats/DailyHeatmap"
import { StreakCalendar } from "@/components/stats/StreakCalendar"
import { HorizontalSessionTimeline } from "@/components/stats/HorizontalSessionTimeline"

interface TodaySession {
  title?: string
  timeRange: string
  duration: number
  completed: boolean
}

interface Session {
  id: string
  title: string
  type: "focus" | "shortBreak" | "longBreak"
  startTime: string
  endTime: string
  completed: boolean
}

interface AnalyticsOverviewTabProps {
  transformedData: {
    dailyFocusTime: Array<{ date: string; minutes: number }>
    completionRate: { completed: number; interrupted: number }
    weeklyComparison: Array<{ name: string; thisWeek: number; lastWeek: number }>
    hourlyDistribution: Array<{ hour: string; value: number }>
    dailyHeatmap: Array<{ day: string; hour: number; value: number }>
    todaySessions: TodaySession[]
    todayQuality: number
    recommendation: string
    streakData: Array<{ date: string; count: number }>
    daysActiveThisMonth: number
    longestStreak: number
    consistencyScore: number
    dailyCalendarSessions: Session[]
  }
  safeStatsData: {
    focusSessions: number
    shortBreakSessions: number
    longBreakSessions: number
    totalSessions: number
    completedSessions: number
    focusDuration: number
    shortBreakDuration: number
    longBreakDuration: number
  }
}

export function AnalyticsOverviewTab({ transformedData, safeStatsData }: AnalyticsOverviewTabProps) {
  return (
    <div className="space-y-6">
      {/* Core Metrics Section - Row 1: Focus Time Trend & Weekly Comparison */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="border-border bg-card/50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Focus Time Trend</CardTitle>
            <CardDescription className="text-sm">Daily focus minutes over the past 30 days</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <FocusTimeChart data={transformedData.dailyFocusTime} />
          </CardContent>
        </Card>

        <Card className="border-border bg-card/50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Weekly Comparison</CardTitle>
            <CardDescription className="text-sm">Compare your focus time across weeks</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <WeeklyComparison data={transformedData.weeklyComparison} />
          </CardContent>
        </Card>
      </div>

      {/* Core Metrics Section - Row 2: Completion Rate & Focus Distribution */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="border-border bg-card/50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Completion Rate</CardTitle>
            <CardDescription className="text-sm">Percentage of completed vs. interrupted sessions</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <CompletionRateChart data={transformedData.completionRate} />
          </CardContent>
        </Card>

        <Card className="border-border bg-card/50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Focus Distribution</CardTitle>
            <CardDescription className="text-sm">When you focus most during the day</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <FocusDistribution data={transformedData.hourlyDistribution} />
          </CardContent>
        </Card>
      </div>

      {/* Daily Patterns Section */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="border-border bg-card/50 shadow-lg md:col-span-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Daily Focus Heatmap</CardTitle>
            <CardDescription className="text-sm">Your focus intensity throughout the day</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <DailyHeatmap data={transformedData.dailyHeatmap} />
          </CardContent>
        </Card>

        <Card className="border-border bg-card/50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Today&apos;s Sessions</CardTitle>
            <CardDescription className="text-sm">Breakdown of today&apos;s focus sessions</CardDescription>
          </CardHeader>
          <CardContent className="h-[280px] overflow-auto pt-0">
            <div className="space-y-3">
              {transformedData.todaySessions.map((session: TodaySession, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-lg bg-accent/30 p-3 transition-all hover:bg-accent/40"
                >
                  <div>
                    <p className="font-medium text-sm">{session.title || `Session ${index + 1}`}</p>
                    <p className="text-xs text-muted-foreground">{session.timeRange}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-primary">{session.duration} min</p>
                    {session.completed ? (
                      <span className="text-xs text-emerald-500">Completed</span>
                    ) : (
                      <span className="text-xs text-amber-500">Interrupted</span>
                    )}
                  </div>
                </div>
              ))}

              {transformedData.todaySessions.length === 0 && (
                <p className="text-center text-muted-foreground py-8 text-sm">No sessions recorded today</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Calendar & Insights Section */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="border-border bg-card/50 shadow-lg md:col-span-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Focus Calendar</CardTitle>
            <CardDescription className="text-sm">Your focus activity over the past 30 days</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <StreakCalendar 
              data={transformedData.streakData} 
              dailyFocusTime={transformedData.dailyFocusTime}
            />
          </CardContent>
        </Card>

        <Card className="border-border bg-card/50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Focus Insights</CardTitle>
            <CardDescription className="text-sm">Patterns and recommendations</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-2 font-medium text-sm">Focus Quality</h4>
                <div className="mb-2 flex items-center">
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div
                      className="h-2 rounded-full bg-emerald-500"
                      style={{ width: `${transformedData.todayQuality}%` }}
                    ></div>
                  </div>
                  <span className="ml-2 text-sm font-medium">{transformedData.todayQuality}%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  {transformedData.todayQuality > 80
                    ? "Excellent focus! You completed most sessions without interruptions."
                    : transformedData.todayQuality > 50
                      ? "Good focus, with some interruptions."
                      : "You had several interruptions. Consider adjusting your environment."}
                </p>
              </div>

              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-2 font-medium text-sm">Consistency Score</h4>
                <div className="mb-2 flex items-center">
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div
                      className="h-2 rounded-full bg-gradient-to-r from-primary to-amber-500"
                      style={{ width: `${transformedData.consistencyScore}%` }}
                    ></div>
                  </div>
                  <span className="ml-2 text-sm font-medium">{transformedData.consistencyScore}%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  {transformedData.consistencyScore > 80
                    ? "Excellent consistency! You're building a strong focus habit."
                    : transformedData.consistencyScore > 50
                      ? "Good consistency. Try to reduce gaps between active days."
                      : "Consider setting a regular schedule for better consistency."}
                </p>
              </div>

              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-2 font-medium text-sm">Recommendation</h4>
                <p className="text-xs text-muted-foreground">{transformedData.recommendation}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Session Statistics Section */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="border-border bg-card/50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Streak Analysis</CardTitle>
            <CardDescription className="text-sm">Your consistency patterns</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">Active Days</span>
                <span className="font-bold text-amber-500">{transformedData.daysActiveThisMonth} days</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">Highest Daily Count</span>
                <span className="font-bold text-emerald-500">{transformedData.streakData.length > 0 ? Math.max(...transformedData.streakData.map(d => d.count)) : 0} sessions</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">Total Focus Sessions</span>
                <span className="font-bold text-primary">{safeStatsData.focusSessions}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">Completed Sessions</span>
                <span className="font-bold text-blue-500">{safeStatsData.completedSessions}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-border bg-card/50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Session Duration</CardTitle>
            <CardDescription className="text-sm">Focus time breakdown</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-1 font-medium text-sm">Total Focus Time</h4>
                <p className="text-xl font-bold">
                  {Math.floor(safeStatsData.focusDuration / 3600)} hrs {Math.floor((safeStatsData.focusDuration % 3600) / 60)} mins
                </p>
                <p className="text-xs text-muted-foreground">
                  Across {safeStatsData.focusSessions} focus sessions
                </p>
              </div>

              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-1 font-medium text-sm">Break Time</h4>
                <p className="text-lg font-bold">
                  {Math.floor((safeStatsData.shortBreakDuration + safeStatsData.longBreakDuration) / 3600)} hrs {Math.floor(((safeStatsData.shortBreakDuration + safeStatsData.longBreakDuration) % 3600) / 60)} mins
                </p>
                <p className="text-xs text-muted-foreground">
                  Short: {Math.floor(safeStatsData.shortBreakDuration / 3600)}h {Math.floor((safeStatsData.shortBreakDuration % 3600) / 60)}m •
                  Long: {Math.floor(safeStatsData.longBreakDuration / 3600)}h {Math.floor((safeStatsData.longBreakDuration % 3600) / 60)}m
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Daily Session Timeline Section */}
      <div className="mt-6 overflow-hidden">
        <HorizontalSessionTimeline
          sessions={transformedData.dailyCalendarSessions}
          date={new Date().toISOString().split("T")[0]}
        />
      </div>
    </div>
  )
}
