'use client';

import React, { useEffect, useCallback } from 'react';
import { useMainPlayer } from './use-main-player';
import { useNatureSounds } from './use-nature-sounds';
import { MainPlayer } from './main-player';
import { NatureSounds } from './nature-sounds';
import { MusicControlProps } from './types';
import { Music } from 'lucide-react';

// Type guards to check playlist types
function hasMusicPlaylist(playlist: MusicControlProps['playlist']): playlist is {
  id: string;
  name: string;
  musics: {
    id: string;
    title: string;
    src?: string | null;
    createdAt?: string;
    updatedAt?: string;
    isPublic?: boolean;
    creatorType?: string;
    userId?: string;
    genres?: string[];
  }[];
  [key: string]: any;
} {
  return playlist !== null && 
         typeof playlist === 'object' && 
         'musics' in playlist && 
         Array.isArray(playlist.musics) && 
         playlist.musics.length > 0;
}

function hasNatureSoundsPlaylist(playlist: MusicControlProps['playlist']): playlist is {
  id: string;
  name: string;
  natureSounds: {
    id: string;
    title: string;
    src?: string | null;
    category?: string[];
  }[];
  [key: string]: any;
} {
  return playlist !== null && 
         typeof playlist === 'object' && 
         'natureSounds' in playlist && 
         Array.isArray(playlist.natureSounds) && 
         playlist.natureSounds.length > 0;
}

interface SystemPlayerProps {
  playlist: MusicControlProps['playlist'];
  className?: string;
  onPlayerRef?: (ref: any) => void;
}

export function SystemPlayer({ playlist, className, onPlayerRef }: SystemPlayerProps) {
  // Initialize main music player with custom hook
  const {
    currentMusicIndex,
    isMainPlaying,
    mainVolume,
    isMainMuted,
    playbackMode,
    togglePlaybackMode,
    isFirstRenderRef,
    playMainTrack,
    toggleMainPlay,
    skipToNext,
    skipToPrevious,
    toggleMainMute,
    handleMainVolumeChange,
    mainAudioRef,
    currentTime,
    duration,
    handleSeek,
  } = useMainPlayer(playlist);

  // Initialize nature sounds with custom hook
  const {
    natureSounds,
    isNatureSoundsExpanded,
    setIsNatureSoundsExpanded,
    audioElementsMapRef,
    toggleNatureSound,
    handleNatureSoundVolume,
    toggleNatureSoundMute,
    initializeNatureSounds,
    cleanupNatureSounds,
    playingNatureSoundsCount
  } = useNatureSounds(playlist);

  // Pause all system audio function
  const pauseAll = useCallback(() => {
    // Pause main audio
    if (mainAudioRef.current && !mainAudioRef.current.paused) {
      mainAudioRef.current.pause();
    }
    
    // Pause all nature sounds
    audioElementsMapRef.current.forEach((audio) => {
      if (!audio.paused) {
        audio.pause();
      }
    });
  }, [mainAudioRef, audioElementsMapRef]);

  // Provide pauseAll function to parent
  useEffect(() => {
    if (onPlayerRef) {
      onPlayerRef({ pauseAll });
    }
  }, [onPlayerRef, pauseAll]);

  // Check if component is visible (using CSS properties check)
  useEffect(() => {
    // Skip this effect on first render to allow auto-play
    if (isFirstRenderRef.current) {
      isFirstRenderRef.current = false;
      return;
    }

    // This effect intentionally does nothing when className changes
    // We want audio to continue playing even when the UI is hidden
  }, [className, isFirstRenderRef]);

  // Load music tracks and nature sounds on mount
  useEffect(() => {
    if (!hasMusicPlaylist(playlist)) return;

    // Cleanup previous audio state
    if (mainAudioRef.current) {
      mainAudioRef.current.pause();
      mainAudioRef.current.onended = null;
      mainAudioRef.current.ontimeupdate = null;
      mainAudioRef.current.src = '';
      mainAudioRef.current = null;
    }

    // Clean up any existing nature sounds
    cleanupNatureSounds();

    // Initialize main music player and nature sounds with a delay to prevent race conditions
    setTimeout(() => {
      // Check if component is still mounted and playlist still has music
      if (hasMusicPlaylist(playlist)) {
        // Use the playMainTrack function directly to properly initialize and start playing
        // This ensures proper state management and audio element creation
        if (playlist.musics[0]) {
          playMainTrack(0); // Start playing the first track (index 0)
        }
        
        // Initialize nature sounds simultaneously with main music
        if (hasNatureSoundsPlaylist(playlist)) {
          initializeNatureSounds(playlist);
        }
      }
    }, 300); // Use same delay as nature sounds for consistency

    // Capture the current map reference to use in cleanup
    const currentAudioElementsMap = audioElementsMapRef.current;

    return () => {
      // Cleanup on unmount
      if (mainAudioRef.current) {
        // Clear any loop check interval
        if (mainAudioRef.current.dataset.loopCheckInterval) {
          clearInterval(Number(mainAudioRef.current.dataset.loopCheckInterval));
        }

        mainAudioRef.current.pause();
        mainAudioRef.current.onended = null;
        mainAudioRef.current.ontimeupdate = null;
        mainAudioRef.current.src = '';
        mainAudioRef.current = null;
      }

      // Cleanup all nature sounds
      cleanupNatureSounds();

      // Clear audio elements map using the captured value
      currentAudioElementsMap.forEach((audio) => {
        audio.pause();
        audio.onended = null;
        audio.ontimeupdate = null;
        audio.src = '';
      });
      currentAudioElementsMap.clear();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [playlist]);

  // Current track info
  const currentTrack = hasMusicPlaylist(playlist) ? playlist.musics[currentMusicIndex] : undefined;
  const musicCount = hasMusicPlaylist(playlist) ? playlist.musics.length : 0;

  // Check if we have any playlist data at all
  const hasAnyPlaylist = hasMusicPlaylist(playlist) || hasNatureSoundsPlaylist(playlist);

  return (
    <div className={`space-y-4 max-h-full overflow-y-auto ${className}`}>
      {/* Show placeholder message when no playlist is available */}
      {!hasAnyPlaylist ? (
        <div className="space-y-4">
          {/* Placeholder message */}
          <div className="bg-card/20 backdrop-blur-sm rounded-xl p-6 shadow-sm border border-border/30 text-center">
            <div className="flex flex-col items-center gap-3">
              <div className="h-12 w-12 bg-muted/50 rounded-lg flex items-center justify-center">
                <Music className="h-6 w-6 text-muted-foreground/50" />
              </div>
              <div>
                <h3 className="font-medium text-foreground mb-1">No System Playlist</h3>
                <p className="text-sm text-muted-foreground leading-relaxed max-w-sm">
                  This video doesn't have any music or nature sounds attached. Try the YouTube or Spotify tabs for streaming music instead.
                </p>
              </div>
            </div>
          </div>
          
          {/* Disabled player interface for consistency */}
          <MainPlayer 
            currentTrackTitle="No tracks available"
            playlistName="No playlist attached"
            currentIndex={0}
            tracksTotal={0}
            isPlaying={false}
            volume={mainVolume}
            isMuted={isMainMuted}
            playbackMode={playbackMode}
            onTogglePlay={() => {}}
            onSkipNext={() => {}}
            onSkipPrevious={() => {}}
            onToggleMute={() => {}}
            onTogglePlaybackMode={() => {}}
            onVolumeChange={() => {}}
            currentTime={0}
            duration={0}
            audioRef={mainAudioRef}
            onSeek={() => {}}
            disabled={true}
          />
        </div>
      ) : (
        <>
          {/* Main Music Player */}
          <MainPlayer 
            currentTrackTitle={currentTrack?.title}
            playlistName={playlist?.name}
            currentIndex={currentMusicIndex}
            tracksTotal={musicCount}
            isPlaying={isMainPlaying}
            volume={mainVolume}
            isMuted={isMainMuted}
            playbackMode={playbackMode}
            onTogglePlay={toggleMainPlay}
            onSkipNext={skipToNext}
            onSkipPrevious={skipToPrevious}
            onToggleMute={toggleMainMute}
            onTogglePlaybackMode={togglePlaybackMode}
            onVolumeChange={handleMainVolumeChange}
            currentTime={currentTime}
            duration={duration}
            audioRef={mainAudioRef}
            onSeek={handleSeek}
            disabled={!hasMusicPlaylist(playlist)}
          />

          {/* Nature Sounds Section */}
          <NatureSounds 
            natureSounds={natureSounds}
            playingCount={playingNatureSoundsCount}
            onToggleSound={toggleNatureSound}
            onVolumeChange={handleNatureSoundVolume}
            onToggleMute={toggleNatureSoundMute}
          />
        </>
      )}
    </div>
  );
} 