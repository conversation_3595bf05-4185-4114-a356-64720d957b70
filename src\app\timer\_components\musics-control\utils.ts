import { Volume1, Volume2, VolumeX } from "lucide-react";
import { NatureSoundPlayer } from "./types";

// Get volume icon based on current volume
export function getVolumeIcon(volume: number, isMuted: boolean) {
  if (isMuted) return VolumeX;
  if (volume === 0) return VolumeX;
  if (volume < 50) return Volume1;
  return Volume2;
}

// Get volume icon for nature sound
export function getNatureSoundVolumeIcon(sound: NatureSoundPlayer) {
  if (sound.isMuted) return VolumeX;
  if (sound.volume === 0) return VolumeX;
  if (sound.volume < 50) return Volume1;
  return Volume2;
} 