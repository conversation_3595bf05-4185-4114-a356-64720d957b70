"use client";

import { useGetAdminNaturePlaylist, useDeleteAdminNaturePlaylist } from "@schemas/NaturalPlaylist/natural-playlist-admin-query";
import { useGetNatureSounds } from "@schemas/Natural/nature-sound-query";
import { useGetVideos } from "@schemas/Video/video-query";
import { notFound, useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { useAddNatureSoundsToNaturePlaylist, useAddVideosToNaturePlaylist } from "@schemas/NaturalPlaylist/nature-playlist-query";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Music2,
  Video,
  Edit,
  Trash2,
  Clock
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import {
  Toolt<PERSON>,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Image from "next/image";

// Import the components
import { MediaTabs } from "./_components";
import { AddItemsDialog } from "./_components";

export default function NaturePlaylistPage() {
  const router = useRouter();
  const params = useParams<{ "nature-playlist-id": string }>();
  const naturePlaylistId = params["nature-playlist-id"];
  const [activeTab, setActiveTab] = useState("videos");

  // Dialog state
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [addDialogType, setAddDialogType] = useState<"nature" | "video">("nature");
  const [searchQuery, setSearchQuery] = useState("");

  // Selection state
  const [selectedNatureSoundIds, setSelectedNatureSoundIds] = useState<string[]>([]);
  const [selectedVideoIds, setSelectedVideoIds] = useState<string[]>([]);

  // Data fetching
  const { data: naturePlaylist, isLoading, refetch } = useGetAdminNaturePlaylist(naturePlaylistId);
  const { data: allNatureSounds } = useGetNatureSounds();
  const { data: allVideos } = useGetVideos();

  // Delete mutation
  const deleteNaturePlaylist = useDeleteAdminNaturePlaylist();

  // Add items mutations
  const addNatureSoundsToNaturePlaylist = useAddNatureSoundsToNaturePlaylist();
  const addVideosToNaturePlaylist = useAddVideosToNaturePlaylist();

  // Extract existing IDs for filtering
  const existingNatureSoundIds = naturePlaylist?.natureSounds?.map((natureSound) => natureSound.id) || [];
  const existingVideoIds = naturePlaylist?.videos?.map(video => video.id) || [];

  // Filter available items that are not already in the playlist
  const availableNatureSounds = allNatureSounds?.filter(natureSound =>
    !existingNatureSoundIds.includes(natureSound.id) &&
    natureSound.title.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  // Filter available videos that are not already in a nature playlist (can have music playlist)
  const availableVideos = allVideos?.filter(video =>
    !video.naturePlaylistId && // Video is not in any nature playlist
    video.title.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  // Calculate total items
  const totalItems = naturePlaylist ?
    naturePlaylist.videos.length + naturePlaylist.natureSounds.length : 0;

  // Set page metadata
  useEffect(() => {
    if (naturePlaylist) {
      document.title = `${naturePlaylist.name} | Pomodoro 365 Admin`;

      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', naturePlaylist.description || `Nature Playlist with ${totalItems} items`);
      } else {
        const meta = document.createElement('meta');
        meta.name = "description";
        meta.content = naturePlaylist.description || `Nature Playlist with ${totalItems} items`;
        document.head.appendChild(meta);
      }

      if (naturePlaylist.imageUrl) {
        const ogImage = document.querySelector('meta[property="og:image"]');
        if (ogImage) {
          ogImage.setAttribute('content', naturePlaylist.imageUrl);
        } else {
          const meta = document.createElement('meta');
          meta.setAttribute('property', 'og:image');
          meta.content = naturePlaylist.imageUrl;
          document.head.appendChild(meta);
        }
      }
    }
  }, [naturePlaylist, totalItems]);

  const handleDelete = async () => {
    try {
      await deleteNaturePlaylist.mutateAsync({ id: naturePlaylistId });
      toast.success("Nature playlist deleted successfully");
      router.push("/admin/nature-playlists");
    } catch {
      toast.error("Failed to delete nature playlist");
    }
  };

  const handleOpenAddDialog = (type: "nature" | "video") => {
    setAddDialogType(type);
    setSearchQuery("");
    setSelectedNatureSoundIds([]);
    setSelectedVideoIds([]);
    setAddDialogOpen(true);
  };

  const handleAddItems = async () => {
    if (!naturePlaylist) return;

    try {
      if (addDialogType === "nature" && selectedNatureSoundIds.length > 0) {
        await addNatureSoundsToNaturePlaylist.mutateAsync({
          naturePlaylistId,
          natureSoundIds: selectedNatureSoundIds
        });
        toast.success(`${selectedNatureSoundIds.length} nature sounds added to playlist`);
        refetch();
      } else if (addDialogType === "video" && selectedVideoIds.length > 0) {
        await addVideosToNaturePlaylist.mutateAsync({
          naturePlaylistId,
          videoIds: selectedVideoIds
        });
        toast.success(`${selectedVideoIds.length} videos added to playlist`);
        refetch();
      }

      setAddDialogOpen(false);
    } catch (error) {
      toast.error(`Failed to update playlist: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleItemSelect = (id: string, checked: boolean) => {
    if (addDialogType === "nature") {
      if (checked) {
        setSelectedNatureSoundIds(prev => [...prev, id]);
      } else {
        setSelectedNatureSoundIds(prev => prev.filter(itemId => itemId !== id));
      }
    } else {
      if (checked) {
        setSelectedVideoIds(prev => [...prev, id]);
      } else {
        setSelectedVideoIds(prev => prev.filter(itemId => itemId !== id));
      }
    }
  };

  const handleSelectAll = () => {
    if (addDialogType === "nature") {
      setSelectedNatureSoundIds(availableNatureSounds.map(natureSound => natureSound.id));
    } else if (addDialogType === "video") {
      setSelectedVideoIds(availableVideos.map(video => video.id));
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4 md:px-6">
        <div className="flex items-center gap-2 mb-8">
          <Button variant="ghost" size="sm" className="gap-1">
            <ArrowLeft className="h-4 w-4" />
            <Skeleton className="h-4 w-20" />
          </Button>
        </div>

        <div className="space-y-8">
          {/* Header skeleton */}
          <div className="relative rounded-xl overflow-hidden">
            <Skeleton className="w-full h-48 md:h-64" />
            <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 to-transparent">
              <div className="space-y-4">
                <Skeleton className="h-8 w-1/2" />
                <Skeleton className="h-4 w-3/4" />
                <div className="flex gap-2">
                  <Skeleton className="h-6 w-16 rounded-full" />
                  <Skeleton className="h-6 w-16 rounded-full" />
                </div>
              </div>
            </div>
          </div>

          {/* Tabs skeleton */}
          <div className="space-y-6">
            <Skeleton className="h-10 w-72" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="aspect-video rounded-lg" />
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!naturePlaylist) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      {/* Back button */}
      <div className="flex items-center gap-2 mb-8">
        <Button
          variant="ghost"
          size="sm"
          className="gap-1 hover:bg-muted"
          onClick={() => router.push("/admin/nature-playlists")}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Nature Playlists
        </Button>
      </div>

      <div className="space-y-8">
        {/* Enhanced Header with Cover Image */}
        <div className="relative rounded-xl overflow-hidden bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/60 z-10" />

          {naturePlaylist.imageUrl && (
            <div className="absolute inset-0">
              <Image
                src={naturePlaylist.imageUrl}
                alt={naturePlaylist.name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 75vw"
              />
            </div>
          )}

          <div className="relative z-20 p-6 pt-20 md:p-8 md:pt-24 lg:p-10 lg:pt-32">
            <div className="max-w-3xl">
              <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-2">
                {naturePlaylist.name}
              </h1>

              {naturePlaylist.description && (
                <p className="text-white/80 text-sm md:text-base mb-4 max-w-xl">
                  {naturePlaylist.description}
                </p>
              )}

              <div className="flex flex-wrap gap-2 mb-6">
                <div className="flex items-center text-xs text-white/70 bg-black/30 backdrop-blur-sm py-1 px-2 rounded-full">
                  <Clock className="h-3 w-3 mr-1" />
                  Created {naturePlaylist.createdAt && format(new Date(naturePlaylist.createdAt), "MMM d, yyyy")}
                </div>

                <div className="flex items-center text-xs text-white/70 bg-black/30 backdrop-blur-sm py-1 px-2 rounded-full">
                  <Video className="h-3 w-3 mr-1" />
                  {naturePlaylist.videos.length} videos
                </div>

                <div className="flex items-center text-xs text-white/70 bg-black/30 backdrop-blur-sm py-1 px-2 rounded-full">
                  <Music2 className="h-3 w-3 mr-1" />
                  {naturePlaylist.natureSounds.length} nature sounds
                </div>

                {naturePlaylist.isDefault && (
                  <Badge variant="secondary" className="bg-yellow-500/80 text-white border-none">
                    Default Playlist
                  </Badge>
                )}
              </div>

              <div className="flex gap-2 flex-wrap">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={() => router.push(`/admin/nature-playlists/${naturePlaylistId}/edit`)}
                        size="sm"
                        variant="secondary"
                        className="bg-white/20 hover:bg-white/30 text-white border-none backdrop-blur-sm"
                        disabled={naturePlaylist.isDefault}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </TooltipTrigger>
                    {naturePlaylist.isDefault && (
                      <TooltipContent>
                        <p>Default playlists cannot be edited</p>
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={handleDelete}
                        size="sm"
                        variant="destructive"
                        className="bg-red-500/70 hover:bg-red-500/90"
                        disabled={naturePlaylist.isDefault}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                    </TooltipTrigger>
                    {naturePlaylist.isDefault && (
                      <TooltipContent>
                        <p>Default playlists cannot be deleted</p>
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </div>

        {/* Media tabs */}
        <MediaTabs
          naturePlaylist={naturePlaylist}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          onAddNature={() => handleOpenAddDialog("nature")}
          onAddVideo={() => handleOpenAddDialog("video")}
          onRefetch={refetch}
        />
      </div>

      {/* Add items dialog */}
      <AddItemsDialog
        open={addDialogOpen}
        onOpenChange={setAddDialogOpen}
        type={addDialogType}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        availableItems={addDialogType === "nature" ? availableNatureSounds : availableVideos}
        selectedIds={addDialogType === "nature" ? selectedNatureSoundIds : selectedVideoIds}
        onSelect={handleItemSelect}
        onSelectAll={handleSelectAll}
        onAdd={handleAddItems}
      />
    </div>
  );
}