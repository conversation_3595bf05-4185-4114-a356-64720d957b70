import { PlaylistDetailView } from "./_components/playlist-detail-view"

interface PlaylistPageProps {
  params: Promise<{
    playlistId: string
  }>
}

export default async function PlaylistPage({ params }: PlaylistPageProps) {
  const { playlistId } = await params
  
  return (
    <div className="container mx-auto py-6 px-4 max-w-7xl">
      <PlaylistDetailView playlistId={playlistId} />
    </div>
  )
} 