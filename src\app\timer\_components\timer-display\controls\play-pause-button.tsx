import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Play, Pause, RotateCcw } from 'lucide-react';
import { memo } from 'react';
import { PlayPauseButtonProps } from '../common/types';
import { TimerColorPreset } from '@/lib/pomodoro-store';

const PlayPauseButton = memo(({
  isRunning,
  onClick,
  onReset,
  timerColor
}: PlayPauseButtonProps) => {
  // If there's no reset button or it's not running (so reset is invisible)
  const shouldCenter = !onReset || !isRunning;

  // Get appropriate color classes based on timer color
  const getColorClasses = (color: TimerColorPreset) => {
    switch (color) {
      case 'white':
        return {
          text: "text-white",
          hoverBg: "hover:bg-black/40"
        };
      case 'blue':
        return {
          text: "text-cyan-100",
          hoverBg: "hover:bg-cyan-900/60"
        };
      case 'orange':
        return {
          text: "text-amber-100",
          hoverBg: "hover:bg-amber-900/60"
        };
      case 'purple':
        return {
          text: "text-purple-100",
          hoverBg: "hover:bg-purple-900/60"
        };
      case 'green':
        return {
          text: "text-green-100",
          hoverBg: "hover:bg-green-900/60"
        };
      default:
        return {
          text: "text-white",
          hoverBg: "hover:bg-black/40"
        };
    }
  };

  const colorClasses = getColorClasses(timerColor);

  return (
    <div className={cn(
      "flex",
      shouldCenter ? "justify-center w-full" : "items-center gap-2"
    )}>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "h-8 px-3 transition-all duration-200 ease-out rounded-xl cursor-pointer",
          colorClasses.text,
          "bg-black/20 backdrop-blur-sm",
          "border border-white/10 hover:border-white/20",
          colorClasses.hoverBg,
          "active:scale-[0.98]",
          isRunning ? "ring-1 ring-white/10" : "",
          // Override any shadowing or white effects from the Button component
          "shadow-none hover:shadow-none",
          "hover:text-white focus:text-white" // Ensure text stays white on all states
        )}
        onClick={onClick}
        title={isRunning ? "Pause timer" : "Start timer"}
        aria-label={isRunning ? "Pause timer" : "Start timer"}
        style={{
          // Add direct styles to override any potential CSS that might be causing white effects
          boxShadow: 'none',
        }}
      >
        <div className="flex items-center gap-1.5 z-10">
          {isRunning ?
            <Pause className="h-3.5 w-3.5" /> :
            <Play className="h-3.5 w-3.5 ml-0.5" />
          }
          <span className="text-xs font-medium">{isRunning ? "Pause" : "Start"}</span>
        </div>
      </Button>

      {onReset && isRunning && (
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "h-8 w-8 transition-all duration-200 ease-out rounded-xl cursor-pointer",
            "bg-black/20 backdrop-blur-sm",
            "border border-white/10 hover:border-white/20",
            colorClasses.text,
            colorClasses.hoverBg,
            "active:scale-[0.98]",
            "ml-0",
            // Override any shadowing or white effects
            "shadow-none hover:shadow-none",
            "hover:text-white focus:text-white" // Ensure text stays white on all states
          )}
          onClick={onReset}
          title="Reset timer"
          aria-label="Reset timer"
          style={{
            boxShadow: 'none',
          }}
        >
          <RotateCcw className="h-3.5 w-3.5" />
        </Button>
      )}
    </div>
  );
});
PlayPauseButton.displayName = 'PlayPauseButton';

export default PlayPauseButton;