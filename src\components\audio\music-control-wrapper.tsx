'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { usePomodoroStore } from '@/lib/pomodoro-store';
import { MusicControl } from '@/app/timer/_components/musics-control/music-control';

export function MusicControlWrapper() {
  const selectedVideo = usePomodoroStore((state) => state.selectedVideo);
  // Audio store is available but not used in this component
  const [isAudioControlsVisible, setIsAudioControlsVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [popoverPosition, setPopoverPosition] = useState<{ top: number; right: number | string; left: number | string }>({ top: 64, right: 16, left: 'auto' });
  const audioControlsRef = useRef<HTMLDivElement>(null);

  // Calculate popover position based on audio toggle button
  const calculatePopoverPosition = useCallback(() => {
    const audioToggleButton = document.querySelector('[data-audio-toggle]') as HTMLElement;
    if (!audioToggleButton) {
      return { top: 64, right: 16, left: 'auto' };
    }

    const buttonRect = audioToggleButton.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Popover dimensions (approximate)
    const popoverHeight = 320; // Approximate height - reduced from 400

    if (isMobile) {
      // Mobile: center the popover horizontally with optimal spacing
      const popoverWidth = 370; // Max width from MusicControl component
      const horizontalPadding = 16; // Minimum padding from screen edges
      const availableWidth = viewportWidth - (horizontalPadding * 2);

      // Use smaller width if screen is too narrow
      const actualPopoverWidth = Math.min(popoverWidth, availableWidth);
      const leftPosition = (viewportWidth - actualPopoverWidth) / 2;

      // Position below button with some spacing, but ensure it fits in viewport
      const topPosition = buttonRect.bottom + 12;
      const maxTop = viewportHeight - popoverHeight - 16;
      const finalTop = Math.min(topPosition, maxTop);

      return {
        top: Math.max(16, finalTop),
        right: 'auto',
        left: leftPosition
      };
    } else {
      // Desktop: position below and to the left of the button
      const popoverWidth = 320; // Max width of the popover - reduced from 400
      const topPosition = buttonRect.bottom + 8;

      // Calculate left position to align popover's right edge with button's right edge
      let leftPosition = buttonRect.right - popoverWidth;

      // Ensure popover doesn't go off-screen on the left
      leftPosition = Math.max(16, leftPosition);

      // Ensure popover doesn't go off-screen on the right
      if (leftPosition + popoverWidth > viewportWidth - 16) {
        leftPosition = viewportWidth - popoverWidth - 16;
      }

      const adjustedTop = Math.min(topPosition, viewportHeight - popoverHeight - 16);

      return {
        top: Math.max(16, adjustedTop),
        right: 'auto',
        left: leftPosition
      };
    }
  }, [isMobile]);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                           window.innerWidth <= 768;
      setIsMobile(isMobileDevice);

      // Recalculate position when mobile state changes
      if (isAudioControlsVisible) {
        setPopoverPosition(calculatePopoverPosition());
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [isMobile, isAudioControlsVisible, calculatePopoverPosition]);

  // Listen for audio controls toggle from video background
  useEffect(() => {
    const handleAudioToggle = (event: CustomEvent<{ visible: boolean }>) => {
      setIsAudioControlsVisible(event.detail.visible);

      // Recalculate position when audio controls are toggled
      if (event.detail.visible) {
        // Use setTimeout to ensure the button is rendered before calculating position
        setTimeout(() => {
          setPopoverPosition(calculatePopoverPosition());
        }, 10);
      }
    };

    // Listen for controls timeout management from video background
    const handleControlsTimeout = (event: CustomEvent<{ hideControls: boolean }>) => {
      if (event.detail.hideControls && !isAudioControlsVisible) {
        // Video background wants to hide controls and audio controls are not visible
        // This is fine, we don't need to do anything special
      }
    };

    window.addEventListener('audio-controls-toggle', handleAudioToggle as EventListener);
    window.addEventListener('video-controls-timeout', handleControlsTimeout as EventListener);

    return () => {
      window.removeEventListener('audio-controls-toggle', handleAudioToggle as EventListener);
      window.removeEventListener('video-controls-timeout', handleControlsTimeout as EventListener);
    };
  }, [isAudioControlsVisible, calculatePopoverPosition]);

  // Handle click outside to close audio controls
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (!isAudioControlsVisible) return;

      const target = event.target as HTMLElement;
      if (!target) return;

      // Check if click is inside the music control wrapper
      if (audioControlsRef.current && audioControlsRef.current.contains(target)) {
        return; // Don't close if clicking inside
      }

      // Check if click is on the audio toggle button (let video background handle this)
      const audioToggleButton = document.querySelector('[data-audio-toggle]');
      if (audioToggleButton && audioToggleButton.contains(target)) {
        return; // Let video background handle the toggle
      }

      // Check if click is on any dropdown, popover, or portal content that might be outside the wrapper
      const isDropdownClick = target.closest('[role="dialog"]') || 
                             target.closest('[role="menu"]') || 
                             target.closest('[role="listbox"]') ||
                             target.closest('[data-radix-portal]') ||
                             target.closest('[data-portal]') ||
                             target.closest('.music-control-container');

      if (isDropdownClick) {
        return; // Don't close if clicking on dropdown/portal content
      }

      // Close the music control
      setIsAudioControlsVisible(false);
      
      // Notify video background that audio controls are closed
      window.dispatchEvent(new CustomEvent('audio-controls-toggle', { 
        detail: { visible: false } 
      }));

      // Also notify video background that it can resume its controls timeout management
      window.dispatchEvent(new CustomEvent('audio-controls-closed', { 
        detail: { shouldResumeTimeout: !isMobile }
      }));
    };

    // Add a small delay to prevent immediate closure on mount
    const timer = setTimeout(() => {
      if (!isMobile) {
        document.addEventListener('mousedown', handleClickOutside);
      } else {
        document.addEventListener('touchstart', handleClickOutside, { passive: true });
      }
    }, 100);
    
    return () => {
      clearTimeout(timer);
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isAudioControlsVisible, isMobile]);

  // Prevent event bubbling when clicking inside the music control
  const handleMusicControlClick = (event: React.MouseEvent | React.TouchEvent) => {
    // Stop propagation to prevent triggering click-outside handlers
    event.stopPropagation();
  };

  // Prevent all event bubbling from music control container
  const handleMusicControlInteraction = (event: React.SyntheticEvent) => {
    // Stop all events from bubbling up
    event.stopPropagation();
  };

  // Always show music control if there's a selected video, even without playlist
  if (!selectedVideo) {
    return null;
  }

  return (
    <div
      className={`fixed z-[9999] transition-opacity duration-300 ${isAudioControlsVisible ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}
      style={{
        top: `${popoverPosition.top}px`,
        right: typeof popoverPosition.right === 'number' ? `${popoverPosition.right}px` : popoverPosition.right,
        left: typeof popoverPosition.left === 'number' ? `${popoverPosition.left}px` : popoverPosition.left,
        zIndex: 9999
      }}
    >
      <div 
        ref={audioControlsRef}
        onClick={handleMusicControlClick}
        onTouchStart={handleMusicControlClick}
        onMouseDown={handleMusicControlInteraction}
        onTouchEnd={handleMusicControlInteraction}
        onPointerDown={handleMusicControlInteraction}
        onPointerUp={handleMusicControlInteraction}
      >
        <MusicControl
          //@ts-expect-error - TODO: fix this
          playlist={selectedVideo.playlist || null}
          className={`${isAudioControlsVisible ? "transition-opacity duration-200" : "opacity-0 transition-opacity duration-200"} ${isMobile ? 'w-full max-w-[370px]' : ''}`}
        />
      </div>
    </div>
  );
} 