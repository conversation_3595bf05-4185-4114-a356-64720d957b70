import React, { useEffect, useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { 
  PlayCircle, 
  PauseCircle, 
  SkipBack, 
  SkipForward, 
  Music, 
  Repeat, 
  Repeat1, 
  Shuffle,
  Volume,
  Volume1,
  Volume2,
  VolumeX
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { PlaybackMode } from './types';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Volume icon helper function
const getVolumeIcon = (volume: number, isMuted: boolean) => {
  if (isMuted || volume === 0) return VolumeX;
  if (volume < 33) return Volume;
  if (volume < 67) return Volume1;
  return Volume2;
};

// Format time (seconds) to MM:SS
const formatTime = (time: number): string => {
  if (isNaN(time)) return "0:00";
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

// Enhanced format time with fixed width - handles up to 99:59 consistently 
const formatTimeFixed = (time: number): string => {
  if (isNaN(time) || time < 0) return "00:00";
  
  const totalMinutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  
  // Handle hours if needed (for very long content)
  if (totalMinutes >= 60) {
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  
  // Standard format with consistent 2-digit padding
  return `${totalMinutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// Add a new CSS style block to the top of the component
const trackContainerStyle = {
  height: '48px', // Increased height for larger text
  overflow: 'hidden'
};

const trackTitleStyle = {
  minHeight: '20px', // Increased for larger text
  display: 'block',
};

const trackInfoStyle = {
  minHeight: '18px', // Increased for larger text
};

interface MainPlayerProps {
  currentTrackTitle?: string;
  playlistName?: string;
  currentIndex: number;
  tracksTotal: number;
  isPlaying: boolean;
  volume: number;
  isMuted: boolean;
  playbackMode: PlaybackMode;
  onTogglePlay: () => void;
  onSkipNext: () => void;
  onSkipPrevious: () => void;
  onToggleMute: () => void;
  onTogglePlaybackMode: () => void;
  onVolumeChange: (value: number[]) => void;
  onSeek?: (time: number) => void;
  currentTime?: number;
  duration?: number;
  audioRef?: React.RefObject<HTMLAudioElement | null>;
  disabled: boolean;
}

export function MainPlayer({
  currentTrackTitle = 'No track selected',
  playlistName = 'Playlist',
  currentIndex,
  tracksTotal,
  isPlaying,
  volume,
  isMuted,
  playbackMode,
  onTogglePlay,
  onSkipNext,
  onSkipPrevious,
  onToggleMute,
  onTogglePlaybackMode,
  onVolumeChange,
  onSeek,
  currentTime: propCurrentTime,
  duration: propDuration,
  audioRef,
  disabled
}: MainPlayerProps) {
  const VolumeIcon = getVolumeIcon(volume, isMuted);
  
  // Track state management
  const [internalCurrentTime, setInternalCurrentTime] = useState(0);
  const [internalDuration, setInternalDuration] = useState(0);
  const prevIndexRef = useRef(currentIndex);
  const progressBarRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLParagraphElement>(null);
  const titleContainerRef = useRef<HTMLDivElement>(null);
  const [isTitleOverflowing, setIsTitleOverflowing] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  
  // Detect mobile/tablet devices
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  // Reset internal time when track changes
  useEffect(() => {
    if (prevIndexRef.current !== currentIndex) {
      setInternalCurrentTime(0);
      prevIndexRef.current = currentIndex;
    }
  }, [currentIndex]);
  
  // Check if title is overflowing
  useEffect(() => {
    const checkOverflow = () => {
      if (titleRef.current && titleContainerRef.current) {
        const isOverflowing = titleRef.current.scrollWidth > titleContainerRef.current.clientWidth;
        setIsTitleOverflowing(isOverflowing);
      }
    };
    
    const timer = setTimeout(checkOverflow, 100);
    window.addEventListener('resize', checkOverflow);
    
    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', checkOverflow);
    };
  }, [currentTrackTitle]);
  
  // Use props values if provided, otherwise use internal state
  const currentTime = propCurrentTime !== undefined ? propCurrentTime : internalCurrentTime;
  const duration = propDuration !== undefined ? propDuration : internalDuration;

  // Update time if using internal state and audioRef is provided
  useEffect(() => {
    if (!audioRef?.current || propCurrentTime !== undefined) return;
    
    const audio = audioRef.current;
    const updateTime = () => {
      setInternalCurrentTime(audio.currentTime);
      if (internalDuration !== audio.duration && !isNaN(audio.duration)) {
        setInternalDuration(audio.duration);
      }
    };
    
    updateTime();
    
    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateTime);
    audio.addEventListener('durationchange', updateTime);
    audio.addEventListener('play', updateTime);
    
    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateTime);
      audio.removeEventListener('durationchange', updateTime);
      audio.removeEventListener('play', updateTime);
    };
  }, [audioRef, propCurrentTime, internalDuration, currentIndex]);

  // Handle seeking when user clicks/drags the progress bar
  const handleProgressChange = (value: number[]) => {
    const newTime = value[0];
    if (onSeek) {
      onSeek(newTime);
    } 
    else if (audioRef?.current) {
      audioRef.current.currentTime = newTime;
    }
  };

  // Get the appropriate playback mode icon and tooltip
  const getPlaybackModeInfo = () => {
    switch(playbackMode) {
      case PlaybackMode.LOOP_ONE:
        return {
          icon: <Repeat1 size={isMobile ? 18 : 16} />,
          tooltip: "Repeat current track"
        };
      case PlaybackMode.SHUFFLE:
        return {
          icon: <Shuffle size={isMobile ? 18 : 16} />,
          tooltip: "Shuffle"
        };
      case PlaybackMode.LOOP_ALL:
      default:
        return {
          icon: <Repeat size={isMobile ? 18 : 16} />,
          tooltip: "Repeat playlist"
        };
    }
  };

  const playbackModeInfo = getPlaybackModeInfo();

  // Handle volume change
  const handleVolumeChange = (value: number[]) => {
    onVolumeChange(value);
  };

  return (
    <div className={cn(
      "bg-card/40 backdrop-blur-sm rounded-xl p-3 border border-border/30",
      "transition-all duration-300 ease-in-out",
      "w-full"
    )}
    >
      {/* Track Info Section */}
      <div className={cn(
        "flex items-center gap-3 mb-3",
        isMobile && "mb-2"
      )}>
        <div className={cn(
          "bg-primary/10 rounded-lg flex items-center justify-center shrink-0",
          isMobile ? "h-12 w-12" : "h-10 w-10"
        )}>
          <Music size={isMobile ? 20 : 16} className="text-primary" />
        </div>
        
        <div 
          className="min-w-0 flex-1 flex flex-col justify-center overflow-hidden"
          style={{ ...trackContainerStyle, width: 'calc(100% - 52px)', maxWidth: 'calc(100% - 52px)' }}
        >
          <div 
            className="overflow-hidden relative h-5"
            ref={titleContainerRef}
          >
            {isTitleOverflowing && isPlaying && (
              <div className="absolute inset-y-0 right-0 w-6 bg-gradient-to-r from-transparent to-card z-10" />
            )}
            {isTitleOverflowing && isPlaying ? (
              <div className="marquee-wrapper overflow-hidden">
                <p 
                  ref={titleRef} 
                  className={cn(
                    "font-medium leading-tight inline-block animate-marquee whitespace-nowrap text-sm"
                  )}
                  style={trackTitleStyle}
                >
                  {currentTrackTitle}
                  <span className="px-3 opacity-70">•</span>
                  {currentTrackTitle}
                </p>
              </div>
            ) : (
              <p 
                ref={titleRef}
                className={cn(
                  "font-medium leading-tight truncate text-sm"
                )}
                style={trackTitleStyle}
              >
                {currentTrackTitle}
              </p>
            )}
          </div>
          
          <p 
            className={cn(
              "text-muted-foreground truncate leading-tight mt-1 h-4 text-xs"
            )}
            style={trackInfoStyle}
          >
            {playlistName} • {currentIndex + 1}/{tracksTotal || 0}
          </p>
        </div>
      </div>

      {/* Progress Bar */}
      <div 
        className={cn(
          "w-full min-w-0 overflow-visible py-2",
          isMobile ? "mb-4" : "mb-3"
        )}
        ref={progressBarRef}
        style={{ maxWidth: '100%' }}
      >
        <Slider
          value={[currentTime]}
          min={0}
          max={duration || 100}
          step={1}
          onValueChange={handleProgressChange}
          className={cn(
            "w-full",
            disabled && "opacity-50",
            "[&_[data-slot=slider-track]]:h-[4px]",
            "[&_[data-slot=slider-thumb]]:h-4 [&_[data-slot=slider-thumb]]:w-4",
            "[&_[data-slot=slider-thumb]]:opacity-100",
            "[&_[data-slot=slider-thumb]]:transition-all",
            "[&_[data-slot=slider-thumb]]:duration-200",
            "[&_[data-slot=slider-thumb]]:border-2",
            "[&_[data-slot=slider-thumb]]:border-red-500",
            "[&_[data-slot=slider-thumb]]:bg-white",
            "[&_[data-slot=slider-thumb]]:shadow-md",
            "[&_[data-slot=slider-track]]:transition-all",
            "[&_[data-slot=slider-track]]:duration-200",
            "[&_[data-slot=slider-range]]:bg-primary",
            "touch-manipulation",
            isMobile && [
              "[&_[data-slot=slider-track]]:h-[6px]",
              "[&_[data-slot=slider-thumb]]:h-5 [&_[data-slot=slider-thumb]]:w-5"
            ]
          )}
          disabled={disabled || duration === 0}
          aria-label="Seek"
        />
        
        <div className="flex justify-between items-center text-xs text-muted-foreground font-mono mt-1">
          <span className="w-14 text-left tabular-nums shrink-0">{formatTimeFixed(currentTime)}</span>
          <span className="w-14 text-right tabular-nums shrink-0">{formatTimeFixed(duration)}</span>
        </div>
      </div>
      
      {/* Mobile Layout: Stacked Controls */}
      {isMobile ? (
        <div className="space-y-3">
          {/* Primary Controls */}
          <div className="flex items-center justify-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-10 w-10 rounded-full",
                !disabled && "hover:bg-primary/10 hover:text-primary",
                "transition-all duration-200 touch-manipulation"
              )}
              onClick={onSkipPrevious}
              disabled={disabled}
              aria-label="Previous track"
            >
              <SkipBack size={18} />
            </Button>

            <Button
              variant="outline"
              size="icon"
              className={cn(
                "h-8 w-8 rounded-full",
                "transition-transform duration-200 hover:scale-105 active:scale-95",
                "shadow-md border touch-manipulation",
                isPlaying
                  ? "text-emerald-600 border-emerald-200 bg-emerald-50 hover:bg-emerald-100"
                  : "text-red-600 border-red-200 bg-red-50 hover:bg-red-100"
              )}
              onClick={onTogglePlay}
              disabled={disabled}
              aria-label={isPlaying ? "Pause" : "Play"}
            >
              {isPlaying 
                ? <PauseCircle size={18} className="transition-opacity duration-200" /> 
                : <PlayCircle size={18} className="transition-opacity duration-200" />
              }
            </Button>

            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-10 w-10 rounded-full",
                !disabled && "hover:bg-primary/10 hover:text-primary",
                "transition-all duration-200 touch-manipulation"
              )}
              onClick={onSkipNext}
              disabled={disabled}
              aria-label="Next track"
            >
              <SkipForward size={18} />
            </Button>
          </div>

          {/* Secondary Controls */}
          <div className="flex items-center justify-between px-2">
            {/* Volume Control */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-8 w-8 p-0 rounded-full shrink-0 touch-manipulation",
                  isMuted ? "text-muted-foreground" : "text-foreground"
                )}
                onClick={onToggleMute}
                aria-label={isMuted ? "Unmute" : "Mute"}
              >
                <VolumeIcon size={16} />
              </Button>
              
              <div className="w-20">
                <Slider
                  value={[volume]}
                  min={0}
                  max={100}
                  step={1}
                  onValueChange={handleVolumeChange}
                  className={cn(
                    "w-full", 
                    disabled && "opacity-50",
                    "[&_[data-slot=slider-track]]:h-[4px]",
                    "[&_[data-slot=slider-thumb]]:h-3 [&_[data-slot=slider-thumb]]:w-3",
                    "[&_[data-slot=slider-range]]:bg-primary",
                    "touch-manipulation"
                  )}
                  disabled={disabled}
                  aria-label="Volume"
                />
              </div>
            </div>

            {/* Playback Mode Button */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-8 w-8 p-0 rounded-full shrink-0 touch-manipulation",
                      !disabled && "hover:bg-primary/10 hover:text-primary",
                      playbackMode !== PlaybackMode.LOOP_ALL && "text-primary"
                    )}
                    onClick={onTogglePlaybackMode}
                    disabled={disabled}
                    aria-label="Change playback mode"
                  >
                    {React.cloneElement(playbackModeInfo.icon)}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  {playbackModeInfo.tooltip}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      ) : (
        /* Desktop Layout: Single Row */
        <div className="flex items-center gap-2 w-full">
          {/* Left Side: Navigation Controls + Playback Mode */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-9 w-9 rounded-full",
                !disabled && "hover:bg-primary/10 hover:text-primary",
                "transition-all duration-200"
              )}
              onClick={onSkipPrevious}
              disabled={disabled}
              aria-label="Previous track"
            >
              <SkipBack size={16} />
            </Button>

            <Button
              variant="outline"
              size="icon"
              className={cn(
                "h-7 w-7 rounded-full mx-1",
                "transition-transform duration-200 hover:scale-105 active:scale-95",
                "shadow-sm border",
                isPlaying
                  ? "text-emerald-600 border-emerald-200 bg-emerald-50 hover:bg-emerald-100"
                  : "text-red-600 border-red-200 bg-red-50 hover:bg-red-100"
              )}
              onClick={onTogglePlay}
              disabled={disabled}
              aria-label={isPlaying ? "Pause" : "Play"}
            >
              {isPlaying 
                ? <PauseCircle size={14} className="transition-opacity duration-200" /> 
                : <PlayCircle size={14} className="transition-opacity duration-200" />
              }
            </Button>

            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-9 w-9 rounded-full",
                !disabled && "hover:bg-primary/10 hover:text-primary",
                "transition-all duration-200"
              )}
              onClick={onSkipNext}
              disabled={disabled}
              aria-label="Next track"
            >
              <SkipForward size={16} />
            </Button>

            {/* Playback Mode Button - Right after navigation */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-8 w-8 p-0 rounded-full shrink-0 ml-2",
                      !disabled && "hover:bg-primary/10 hover:text-primary",
                      playbackMode !== PlaybackMode.LOOP_ALL && "text-primary"
                    )}
                    onClick={onTogglePlaybackMode}
                    disabled={disabled}
                    aria-label="Change playback mode"
                  >
                    {React.cloneElement(playbackModeInfo.icon)}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  {playbackModeInfo.tooltip}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          {/* Right Side: Volume Control */}
          <div className="flex items-center gap-2 ml-auto">
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "h-8 w-8 p-0 rounded-full shrink-0",
                isMuted ? "text-muted-foreground" : "text-foreground",
                "hover:bg-primary/10"
              )}
              onClick={onToggleMute}
              aria-label={isMuted ? "Unmute" : "Mute"}
            >
              <VolumeIcon size={14} />
            </Button>
            
            <div className="w-16">
              <Slider
                value={[volume]}
                min={0}
                max={100}
                step={1}
                onValueChange={handleVolumeChange}
                className={cn(
                  "w-full", 
                  disabled && "opacity-50",
                  "[&_[data-slot=slider-track]]:h-[3px]",
                  "[&_[data-slot=slider-thumb]]:h-3 [&_[data-slot=slider-thumb]]:w-3",
                  "[&_[data-slot=slider-range]]:bg-primary",
                  "[&_[data-slot=slider-track]]:bg-muted",
                  "hover:[&_[data-slot=slider-track]]:h-[4px]",
                  "[&_[data-slot=slider-track]]:transition-all",
                  "[&_[data-slot=slider-track]]:duration-200"
                )}
                disabled={disabled}
                aria-label="Volume"
              />
            </div>
            
            <span className="text-xs w-6 text-right text-muted-foreground font-mono tabular-nums">
              {volume}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}