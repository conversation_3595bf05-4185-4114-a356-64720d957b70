"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  CheckSquare, 
  Plus, 
  Clock, 
  Flag, 
  CheckCircle, 
  Circle,
  Calendar,
  Target,
  TrendingUp,
  ClipboardList
} from "lucide-react"
import { cn } from "@/lib/utils"

interface TasksContentProps {
  activeTab: string
}

export function TasksContent({ activeTab }: TasksContentProps) {
  const renderTaskOverview = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Task Overview</h2>
          <p className="text-muted-foreground">Monitor your task completion and productivity</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Task
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
            <ClipboardList className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">+2 from yesterday</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18</div>
            <p className="text-xs text-muted-foreground">75% completion rate</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">Active tasks</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Priority</CardTitle>
            <Flag className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">Urgent tasks</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Tasks */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Tasks</CardTitle>
          <CardDescription>Your latest task activity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { title: "Complete project proposal", status: "completed", priority: "high", pomodoros: 3 },
              { title: "Review design mockups", status: "in-progress", priority: "medium", pomodoros: 1 },
              { title: "Update documentation", status: "pending", priority: "low", pomodoros: 0 },
              { title: "Team meeting preparation", status: "completed", priority: "medium", pomodoros: 2 },
            ].map((task, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                <div className="flex items-center gap-3">
                  {task.status === "completed" ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <Circle className="h-5 w-5 text-muted-foreground" />
                  )}
                  <div>
                    <p className={cn("font-medium", task.status === "completed" && "line-through text-muted-foreground")}>
                      {task.title}
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge 
                        variant={task.priority === "high" ? "destructive" : task.priority === "medium" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {task.priority}
                      </Badge>
                      <span className="text-xs text-muted-foreground">{task.pomodoros} pomodoros</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderCompletedTasks = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Completed Tasks</h2>
        <p className="text-muted-foreground">Tasks you've successfully finished</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Completed This Week
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              { title: "Design system documentation", completedAt: "2 hours ago", pomodoros: 4 },
              { title: "Code review for feature X", completedAt: "Yesterday", pomodoros: 2 },
              { title: "Client presentation prep", completedAt: "2 days ago", pomodoros: 3 },
              { title: "Bug fixes for mobile app", completedAt: "3 days ago", pomodoros: 5 },
            ].map((task, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="font-medium">{task.title}</p>
                    <p className="text-sm text-muted-foreground">{task.completedAt}</p>
                  </div>
                </div>
                <Badge variant="outline" className="text-green-700 border-green-300">
                  {task.pomodoros} pomodoros
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderPriorityTasks = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Priority Tasks</h2>
        <p className="text-muted-foreground">High-priority items requiring immediate attention</p>
      </div>

      <div className="space-y-4">
        {[
          { title: "Fix critical production bug", priority: "urgent", deadline: "Today", progress: 60 },
          { title: "Prepare quarterly report", priority: "high", deadline: "Tomorrow", progress: 30 },
          { title: "Client demo preparation", priority: "high", deadline: "This week", progress: 80 },
        ].map((task, index) => (
          <Card key={index} className="border-l-4 border-l-red-500">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">{task.title}</h3>
                  <div className="flex items-center gap-2">
                    <Badge variant="destructive" className="text-xs">
                      {task.priority}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      <Calendar className="h-3 w-3 mr-1" />
                      {task.deadline}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Progress</span>
                    <span>{task.progress}%</span>
                  </div>
                  <Progress value={task.progress} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  switch (activeTab) {
    case "task-overview":
      return renderTaskOverview()
    case "completed-tasks":
      return renderCompletedTasks()
    case "priority-tasks":
      return renderPriorityTasks()
    default:
      return renderTaskOverview()
  }
}
