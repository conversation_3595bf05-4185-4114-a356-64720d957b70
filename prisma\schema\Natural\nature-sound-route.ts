import { <PERSON><PERSON> } from "hono";
import { zValidator } from "@hono/zod-validator";
import { createNatureSoundSchema, updateNatureSoundSchema } from "./nature-sound-type";
import prisma from "@/lib/prisma";
import { UserVariable } from "../..";
import { UserRole } from "@prisma/client";
import { privateRoutesMiddleware } from "@/server/private/middleware";

const app = new Hono<{ Variables: UserVariable }>()
  // Get all nature sounds
  .get("/", async (c) => {
    // We don't use the user variable in this route
    const isPublic = c.req.query("isPublic") === "true" ? true : c.req.query("isPublic") === "false" ? false : undefined;
    const category = c.req.query("category");
    const playlistId = c.req.query("playlistId");

    console.log({
      isPublic,
      category,
      playlistId,
    });

    const filters: Record<string, unknown> = {};

    // Add filters based on query parameters
    if (isPublic !== undefined) {
      filters.isPublic = isPublic;
    }

    if (category) {
      filters.category = {
        has: category
      };
    }

    // Instead of directly filtering by playlistId, we'll need to adjust our query
    const whereCondition: any = { ...filters };

    const natureSounds = await prisma.natureSound.findMany({
      where: playlistId ? {
        ...whereCondition,
        naturePlaylists: {
          some: {
            id: playlistId
          }
        }
      } : whereCondition,
      include: {
        naturePlaylists: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc'
      },
    });

    return c.json({ data: natureSounds });
  })

  // Get single nature sound
  .get("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    const natureSound = await prisma.natureSound.findUnique({
      where: { id },
      include: {
        naturePlaylists: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!natureSound) {
      return c.json({ error: "Nature sound not found" }, 404);
    }

    // Check if user can access this nature sound
    const canAccess =
      natureSound.isPublic ||
      natureSound.creatorType === UserRole.ADMIN ||
      natureSound.userId === user.id;

    if (!canAccess) {
      return c.json({ error: "Unauthorized" }, 403);
    }

    return c.json({ data: natureSound });
  })

  // Create nature sound - only admin can create
  .post("/", privateRoutesMiddleware, zValidator("form", createNatureSoundSchema), async (c) => {
    const user = c.get("user");
    
    // Check if user is admin
    if (user.role !== UserRole.ADMIN) {
      return c.json({ error: "Only admins can create nature sounds" }, 403);
    }

    const { title, src, isPublic, category, playlistId } = c.req.valid("form");

    // Create nature sound in database
    const natureSound = await prisma.natureSound.create({
      data: {
        title,
        src: src || "",
        isPublic: isPublic ?? false,
        category: category || [],
        userId: user.id,
        creatorType: user.role as UserRole, // Set creatorType based on user's role
        ...(playlistId ? {
          naturePlaylists: {
            connect: {
              id: playlistId
            }
          }
        } : {})
      },
    });

    return c.json({ data: natureSound });
  })

  // Update nature sound - only owner or admin can update admin-created content
  .patch("/:id", privateRoutesMiddleware, zValidator("form", updateNatureSoundSchema), async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");
    const updates = c.req.valid("form");

    // First check if nature sound exists
    const existingNatureSound = await prisma.natureSound.findUnique({
      where: { id },
    });

    if (!existingNatureSound) {
      return c.json({ error: "Nature sound not found" }, 404);
    }

    // Security check: Only allow updates if:
    // 1. User owns the nature sound, OR
    // 2. User is admin (can update any nature sound), OR
    // 3. Admin-created content can only be updated by admins
    const canUpdate = 
      existingNatureSound.userId === user.id ||
      (user.role === UserRole.ADMIN) ||
      (existingNatureSound.creatorType === UserRole.ADMIN && user.role !== UserRole.ADMIN ? false : true);

    if (!canUpdate || (existingNatureSound.creatorType === UserRole.ADMIN && user.role !== UserRole.ADMIN)) {
      return c.json({ error: "Unauthorized to update this nature sound" }, 403);
    }

    // Prepare update data with proper typing
    const updateData = {
      ...(updates.title !== undefined && { title: updates.title }),
      ...(updates.src !== undefined && { src: updates.src }),
      ...(updates.description !== undefined && { description: updates.description }),
      ...(updates.isPublic !== undefined && { isPublic: updates.isPublic }),
      ...(updates.category !== undefined && { category: updates.category }),
    };

    // Handle playlist connection/disconnection if needed
    let playlistUpdateOperation = {};
    if (updates.playlistId !== undefined) {
      if (updates.playlistId) {
        // Connect to the specified playlist
        playlistUpdateOperation = {
          naturePlaylists: {
            connect: {
              id: updates.playlistId
            }
          }
        };
      } else {
        // If playlistId is null or empty, disconnect from any playlists
        // This would require fetching existing playlists and disconnecting them
        const existingPlaylists = await prisma.naturePlaylist.findMany({
          where: {
            natureSounds: {
              some: {
                id
              }
            }
          }
        });

        if (existingPlaylists.length > 0) {
          playlistUpdateOperation = {
            naturePlaylists: {
              disconnect: existingPlaylists.map((playlist) => ({ id: playlist.id }))
            }
          };
        }
      }
    }

    // Update the nature sound
    const updatedNatureSound = await prisma.natureSound.update({
      where: { id },
      data: {
        ...updateData,
        ...playlistUpdateOperation
      },
    });

    return c.json({ data: updatedNatureSound });
  })

  // Delete nature sound - only admin can delete
  .delete("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    // Check if user is admin
    if (user.role !== UserRole.ADMIN) {
      return c.json({ error: "Only admins can delete nature sounds" }, 403);
    }

    // Find the nature sound
    const natureSound = await prisma.natureSound.findUnique({
      where: { id },
    });

    if (!natureSound) {
      return c.json({ error: "Nature sound not found" }, 404);
    }

    // Delete nature sound
    await prisma.natureSound.delete({
      where: { id },
    });

    return c.json({ success: true });
  });

export default app;