import { z } from "zod";
import { MusicPlaylistSchema, MusicPlaylistPartialSchema } from "@types";

// Regular user create schema - only name field
export const createMusicPlaylistSchema = z.object({
  name: z.string().min(1, "Name is required"),
});

// Admin create schema - includes all fields
export const createMusicPlaylistSchemaAdmin = MusicPlaylistSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
  isDefault: true,
  musicOrder: true,
}).extend({
  imageUrl: z
    .string()
    .url()
    .transform((value) => (value === "" ? undefined : value))
    .optional().nullable(),
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional().default(false),
  description: z.string().optional(),
  musicIds: z.array(z.string()).optional(),
  videoIds: z.array(z.string()).optional(),
});

// Regular user update schema - only name field
export const updateMusicPlaylistSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
});

// Admin update schema - includes all fields
export const updateMusicPlaylistSchemaAdmin = MusicPlaylistPartialSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
  isDefault: true,
}).extend({
  imageUrl: z
    .string()
    .url()
    .transform((value) => (value === "" ? undefined : value))
    .optional().nullable(),
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional().default(false),
  description: z.string().optional(),
  musicIds: z.preprocess(
    (val) => Array.isArray(val) ? val : typeof val === 'string' ? [val] : val,
    z.array(z.string()).optional()
  ),
  videoIds: z.preprocess(
    (val) => Array.isArray(val) ? val : typeof val === 'string' ? [val] : val,
    z.array(z.string()).optional()
  ),
});

export type CreateMusicPlaylistInput = z.infer<typeof createMusicPlaylistSchema>;
export type CreateMusicPlaylistAdminInput = z.infer<typeof createMusicPlaylistSchemaAdmin>;
export type UpdateMusicPlaylistInput = z.infer<typeof updateMusicPlaylistSchema>;
export type UpdateMusicPlaylistAdminInput = z.infer<typeof updateMusicPlaylistSchemaAdmin>; 