'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { YouTubePlayerState } from './types';

declare global {
  interface Window {
    YT: any;
    onYouTubeIframeAPIReady: () => void;
  }
}

interface UseYouTubePlayerProps {
  onPlayStart?: () => void;
}

export function useYouTubePlayer({ onPlayStart }: UseYouTubePlayerProps = {}) {
  const [playerState, setPlayerState] = useState<YouTubePlayerState>({
    currentVideoId: '', // No default video ID to prevent auto-creation
    isPlaying: false,
    volume: 70,
    isMuted: false,
    inputUrl: 'https://www.youtube.com/watch?v=hlWiI4xVXKY'
  });

  const playerRef = useRef<any>(null);
  const isAPILoadedRef = useRef(false);
  const isPlayerReadyRef = useRef(false);

  // Extract video ID from YouTube URL
  const extractVideoId = useCallback((url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /^([a-zA-Z0-9_-]{11})$/ // Direct video ID
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  }, []);

  const createPlayer = useCallback(() => {
    if (!window.YT || !window.YT.Player || !playerState.currentVideoId) return;

    try {
      // Destroy existing player if it exists
      if (playerRef.current && typeof playerRef.current.destroy === 'function') {
        playerRef.current.destroy();
      }

      playerRef.current = new window.YT.Player('youtube-player', {
        height: '100%',
        width: '100%',
        videoId: playerState.currentVideoId,
        playerVars: {
          autoplay: 0,
          controls: 1,
          modestbranding: 1,
          rel: 0,
          showinfo: 0,
          iv_load_policy: 3,
          disablekb: 0,
          fs: 1,
          cc_load_policy: 0,
          origin: window.location.origin,
          enablejsapi: 1,
        },
        events: {
          onReady: (event: any) => {
            isPlayerReadyRef.current = true;
            event.target.setVolume(playerState.volume);
            if (playerState.isMuted) {
              event.target.mute();
            }
          },
          onStateChange: (event: any) => {
            const isCurrentlyPlaying = event.data === window.YT.PlayerState.PLAYING;
            const wasPlaying = playerState.isPlaying;
            
            setPlayerState(prev => ({ ...prev, isPlaying: isCurrentlyPlaying }));
            
            // Trigger callback when video starts playing (transition from not playing to playing)
            if (isCurrentlyPlaying && !wasPlaying && onPlayStart) {
              onPlayStart();
            }
          },
          onError: (event: any) => {
            console.error('YouTube player error:', event.data);
            isPlayerReadyRef.current = false;
          }
        },
      });
    } catch (error) {
      console.error('Error creating YouTube player:', error);
      isPlayerReadyRef.current = false;
    }
  }, [playerState.currentVideoId, playerState.volume, playerState.isMuted, playerState.isPlaying, onPlayStart]);

  // Load YouTube IFrame API but don't create player yet
  useEffect(() => {
    if (isAPILoadedRef.current) return;

    const loadYouTubeAPI = () => {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag);
    };

    if (!window.YT) {
      loadYouTubeAPI();
    }

    window.onYouTubeIframeAPIReady = () => {
      isAPILoadedRef.current = true;
      // Don't auto-create player here - wait for user to load a video
    };

    if (window.YT && window.YT.Player) {
      isAPILoadedRef.current = true;
      // Don't auto-create player here - wait for user to load a video
    }

    return () => {
      if (playerRef.current && typeof playerRef.current.destroy === 'function') {
        try {
          playerRef.current.destroy();
        } catch (error) {
          console.error('Error destroying YouTube player:', error);
        }
      }
      isPlayerReadyRef.current = false;
    };
  }, []); // Remove createPlayer dependency

  const togglePlay = useCallback(() => {
    if (!playerRef.current || !isPlayerReadyRef.current) return;

    try {
      if (playerState.isPlaying) {
        playerRef.current.pauseVideo();
      } else {
        // Only call onPlayStart when manually starting playback
        if (onPlayStart) {
          onPlayStart();
        }
        playerRef.current.playVideo();
      }
    } catch (error) {
      console.error('Error toggling YouTube player:', error);
    }
  }, [playerState.isPlaying, onPlayStart]);

  const handleVolumeChange = useCallback((volume: number) => {
    setPlayerState(prev => ({ ...prev, volume, isMuted: false }));
    if (playerRef.current && isPlayerReadyRef.current) {
      try {
        playerRef.current.setVolume(volume);
        if (volume > 0) {
          playerRef.current.unMute();
        }
      } catch (error) {
        console.error('Error changing YouTube player volume:', error);
      }
    }
  }, []);

  const toggleMute = useCallback(() => {
    setPlayerState(prev => ({ ...prev, isMuted: !prev.isMuted }));
    if (playerRef.current && isPlayerReadyRef.current) {
      try {
        if (playerState.isMuted) {
          playerRef.current.unMute();
        } else {
          playerRef.current.mute();
        }
      } catch (error) {
        console.error('Error toggling YouTube player mute:', error);
      }
    }
  }, [playerState.isMuted]);

  const handleUrlChange = useCallback((url: string) => {
    setPlayerState(prev => ({ ...prev, inputUrl: url }));
  }, []);

  const loadVideo = useCallback(() => {
    const videoId = extractVideoId(playerState.inputUrl);
    if (!videoId) {
      alert('Please enter a valid YouTube URL');
      return;
    }

    setPlayerState(prev => ({ ...prev, currentVideoId: videoId }));
    
    // Check if player exists and is ready
    if (playerRef.current && isPlayerReadyRef.current && typeof playerRef.current.loadVideoById === 'function') {
      try {
        // Stop current video first to prevent audio overlap
        if (typeof playerRef.current.stopVideo === 'function') {
          playerRef.current.stopVideo();
        }
        
        // Load the new video without recreating the player
        playerRef.current.loadVideoById({
          videoId: videoId,
          startSeconds: 0
        });
        
        // Set volume and mute state for new video
        setTimeout(() => {
          if (playerRef.current && isPlayerReadyRef.current) {
            playerRef.current.setVolume(playerState.volume);
            if (playerState.isMuted) {
              playerRef.current.mute();
            }
            
            // Auto-play the new video and pause system audio when it starts
            if (typeof playerRef.current.playVideo === 'function') {
              if (onPlayStart) {
                onPlayStart();
              }
              playerRef.current.playVideo();
            }
          }
        }, 500);
      } catch (error) {
        console.error('Error loading YouTube video:', error);
        // Fallback: recreate player with new video only if loadVideoById fails
        createPlayer();
      }
    } else {
      // Player doesn't exist or not ready, create it
      if (isAPILoadedRef.current) {
        createPlayer();
      }
    }
  }, [playerState.inputUrl, playerState.volume, playerState.isMuted, extractVideoId, createPlayer, onPlayStart]);

  // Create player when currentVideoId changes (after loadVideo sets it)
  useEffect(() => {
    if (playerState.currentVideoId && isAPILoadedRef.current && !playerRef.current) {
      createPlayer();
    }
  }, [playerState.currentVideoId, createPlayer]);

  const pauseVideo = useCallback(() => {
    if (playerRef.current && isPlayerReadyRef.current && typeof playerRef.current.pauseVideo === 'function') {
      try {
        playerRef.current.pauseVideo();
      } catch (error) {
        console.error('Error pausing YouTube video:', error);
      }
    }
  }, []);

  return {
    playerState,
    togglePlay,
    handleVolumeChange,
    toggleMute,
    handleUrlChange,
    loadVideo,
    pauseVideo,
    playerRef,
    isPlayerReady: isPlayerReadyRef.current,
  };
} 