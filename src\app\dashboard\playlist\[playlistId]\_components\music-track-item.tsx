"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"

import { Play, Pause, MoreHorizontal, Trash2, Star, Clock } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { useRemoveMusicFromMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { toast } from "sonner"
// Define a more flexible type for music from playlist
interface PlaylistMusic {
  id: string
  title: string
  src?: string | null
  source?: any
  rating?: number | null
  genres?: string[]
  user?: {
    id: string
    name: string
    image: string | null
  }
}

interface MusicTrackItemProps {
  music: PlaylistMusic
  index: number
  isPlaying: boolean
  onPlay: () => void
  playlistId: string
}

export function MusicTrackItem({ music, index, isPlaying, onPlay, playlistId }: MusicTrackItemProps) {
  const [isHovered, setIsHovered] = useState(false)
  const removeMusicFromPlaylist = useRemoveMusicFromMusicPlaylistUser()

  const handleRemove = async () => {
    try {
      await removeMusicFromPlaylist.mutateAsync({
        musicPlaylistUserId: playlistId,
        musicId: music.id
      })
      toast.success("Track removed from playlist")
    } catch (error) {
      console.error(error)
      toast.error("Failed to remove track")
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return "3:30" // Default duration
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }



  return (
    <motion.div
      layout
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 10 }}
      transition={{ duration: 0.2, delay: index * 0.05 }}
      className={cn(
        "group flex items-center gap-4 px-4 py-3 mx-2 hover:bg-muted/30 transition-all duration-200 rounded-lg",
        isPlaying && "bg-orange-50/50 dark:bg-orange-950/20"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Track Number / Play Button */}
      <div className="w-8 h-8 flex items-center justify-center shrink-0">
        {isHovered || isPlaying ? (
          <Button
            variant="ghost"
            size="icon"
            onClick={onPlay}
            className={cn(
              "h-8 w-8 rounded-full transition-all duration-200",
              isPlaying
                ? "bg-orange-500 text-white hover:bg-orange-600"
                : "hover:bg-orange-100 dark:hover:bg-orange-900/50"
            )}
          >
            {isPlaying ? (
              <Pause className="h-3.5 w-3.5" />
            ) : (
              <Play className="h-3.5 w-3.5 ml-0.5" />
            )}
          </Button>
        ) : (
          <span className="text-sm text-muted-foreground font-medium">
            {(index + 1).toString().padStart(2, '0')}
          </span>
        )}
      </div>

      {/* Track Info */}
      <div className="flex-1 min-w-0 flex flex-col justify-center space-y-1">
        <div className="flex items-center gap-2">
          <h4 className={cn(
            "font-medium text-sm truncate transition-colors duration-200 leading-tight",
            isPlaying && "text-orange-600 dark:text-orange-400"
          )}>
            {music.title}
          </h4>
          {music.rating && music.rating > 4 && (
            <Star className="h-3 w-3 text-yellow-500 fill-current shrink-0" />
          )}
        </div>

        <div className="flex items-center gap-1.5 text-xs text-muted-foreground leading-tight">
          <span>Track</span>
          {music.source && (
            <>
              <span>•</span>
              <span className="truncate max-w-24">{music.source}</span>
            </>
          )}
          {music.rating && (
            <>
              <span>•</span>
              <div className="flex items-center gap-0.5">
                <Star className="h-2.5 w-2.5 text-yellow-500 fill-current" />
                <span>{music.rating}</span>
              </div>
            </>
          )}
          {/* Compact Genres */}
          {music.genres && music.genres.length > 0 && (
            <>
              <span>•</span>
              <span className="truncate max-w-20">{music.genres[0]}</span>
              {music.genres.length > 1 && (
                <span>+{music.genres.length - 1}</span>
              )}
            </>
          )}
        </div>
      </div>

      {/* Duration & Actions Combined */}
      <div className="flex items-center gap-3 shrink-0">
        <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
          <Clock className="h-3 w-3" />
          <span className="font-medium">{formatDuration()}</span>
        </div>

        {/* Actions */}
        <div className={cn(
          "flex items-center transition-opacity duration-200",
          isHovered || isPlaying ? "opacity-100" : "opacity-0"
        )}>
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-muted"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-44">
              <DropdownMenuItem onClick={onPlay}>
                <Play className="mr-2 h-4 w-4" />
                {isPlaying ? "Restart Track" : "Play Track"}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleRemove}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Remove from Playlist
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </motion.div>
  )
} 