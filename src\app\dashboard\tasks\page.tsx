"use client"

import { useState } from "react"
import { TasksContent } from "@/components/stats/TasksContent"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

export default function TasksPage() {
  const [activeTab, setActiveTab] = useState("task-overview")

  return (
    <div className="flex-1 overflow-y-auto overflow-x-hidden">
      <div className="w-full max-w-[90rem] mx-auto p-4 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="task-overview">Overview</TabsTrigger>
            <TabsTrigger value="completed-tasks">Completed</TabsTrigger>
            <TabsTrigger value="priority-tasks">Priority</TabsTrigger>
          </TabsList>
          
          <TabsContent value="task-overview" className="mt-6">
            <TasksContent activeTab="task-overview" />
          </TabsContent>

          <TabsContent value="completed-tasks" className="mt-6">
            <TasksContent activeTab="completed-tasks" />
          </TabsContent>

          <TabsContent value="priority-tasks" className="mt-6">
            <TasksContent activeTab="priority-tasks" />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
} 