"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MoreHorizontal, Play, PauseCircle } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { useDeleteNatureSound } from "@schemas/Natural/nature-sound-query";
import { toast } from "sonner";
import { GetNatureSounds_ResponseTypeSuccess } from "@schemas/Natural/nature-sound-query";
import { useState } from "react";
import { NatureSoundFormSheet } from "./nature-sound-form-sheet";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useAudioStore } from "@/lib/audio-store";
import { TableCell, TableRow } from "@/components/ui/table";
import { formatDate } from "@/lib/utils";

interface NatureSoundRowProps {
  natureSound: GetNatureSounds_ResponseTypeSuccess[number];
}

export function NatureSoundRow({ natureSound }: NatureSoundRowProps) {
  const router = useRouter();
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
  const { mutateAsync: deleteNatureSound } = useDeleteNatureSound();
  const { selectedAudioId, setSelectedAudioId } = useAudioStore();

  const isPlaying = selectedAudioId === natureSound.id;

  const handleDelete = async () => {
    try {
      await deleteNatureSound({ id: natureSound.id });
      toast.success("Nature sound deleted successfully");
      router.refresh();
    } catch {
      toast.error("Failed to delete nature sound");
    }
  };

  const handlePlayToggle = () => {
    if (isPlaying) {
      setSelectedAudioId("");
    } else {
      setSelectedAudioId(natureSound.id);
    }
  };

  return (
    <>
      <TableRow>
        <TableCell className="font-medium">{natureSound.title}</TableCell>
        <TableCell>
          {natureSound.category && natureSound.category.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {natureSound.category.map((cat, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {cat}
                </Badge>
              ))}
            </div>
          )}
        </TableCell>
        <TableCell>{formatDate(natureSound.createdAt)}</TableCell>
        <TableCell>{natureSound.isPublic ? "Public" : "Private"}</TableCell>
        <TableCell>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handlePlayToggle}
              className="h-8 w-8"
            >
              {isPlaying ? (
                <PauseCircle className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5" />
              )}
            </Button>

            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setIsEditSheetOpen(true)}>
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <DropdownMenuItem
                      onSelect={(e) => e.preventDefault()}
                      className="text-destructive"
                    >
                      Delete
                    </DropdownMenuItem>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Nature Sound</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete this nature sound? This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDelete}
                        className="bg-destructive hover:bg-destructive/90"
                      >
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </TableCell>
      </TableRow>

      <NatureSoundFormSheet
        open={isEditSheetOpen}
        onOpenChange={setIsEditSheetOpen}
        natureSoundId={natureSound.id}
      />
    </>
  );
}