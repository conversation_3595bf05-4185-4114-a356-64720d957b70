import { <PERSON><PERSON> } from "hono";
import { z<PERSON>alidator } from "@hono/zod-validator";
import { createMusicPlaylistUserSchema, updateMusicPlaylistUserSchema } from "./music-playlist-user-type";
import prisma from "@/lib/prisma";
import { UserVariable } from "../..";
import { z } from "zod";
import { privateRoutesMiddleware } from "@/server/private/middleware";

const app = new Hono<{ Variables: UserVariable }>()
  // Get user music playlists
  .get("/", privateRoutesMiddleware, async (c) => {
    try {
      const user = c.get("user");
      const isPublic = c.req.query("isPublic") === "true" ? true : c.req.query("isPublic") === "false" ? false : undefined;

      const musicPlaylistsUser = await prisma.musicPlaylistUser.findMany({
        where: {
          userId: user.id,
          ...(isPublic !== undefined && { isPublic }),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          musics: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          natureSounds: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          videos: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return c.json({
        data: musicPlaylistsUser,
      });
    } catch (error) {
      console.error("Error fetching user music playlists:", error);
      return c.json(
        { error: "Failed to fetch user music playlists" },
        500
      );
    }
  })
  
  // Create user music playlist
  .post("/", privateRoutesMiddleware, zValidator("form", createMusicPlaylistUserSchema), async (c) => {
    try {
      const user = c.get("user");
      const { name, description, imageUrl, isPublic, musicIds, natureSoundIds, videoIds } = c.req.valid("form");

      // Create relationships with musics, natural sounds and videos if provided
      const connectMusics = musicIds && musicIds.length > 0
        ? { connect: musicIds.map(id => ({ id })) }
        : undefined;

      const connectNatureSounds = natureSoundIds && natureSoundIds.length > 0
        ? { connect: natureSoundIds.map(id => ({ id })) }
        : undefined;

      const connectVideos = videoIds && videoIds.length > 0
        ? { connect: videoIds.map(id => ({ id })) }
        : undefined;

      const musicPlaylistUser = await prisma.musicPlaylistUser.create({
        data: {
          name,
          description,
          imageUrl,
          isPublic: isPublic || false,
          userId: user.id,
          musicOrder: musicIds || [],
          natureSoundOrder: natureSoundIds || [],
          musics: connectMusics,
          natureSounds: connectNatureSounds,
          videos: connectVideos,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          musics: true,
          natureSounds: true,
          videos: true,
        },
      });

      return c.json({
        data: musicPlaylistUser,
      });
    } catch (error) {
      console.error("Error creating user music playlist:", error);
      return c.json(
        { error: "Failed to create user music playlist" },
        500
      );
    }
  })
  
  // Get single user music playlist
  .get("/:music-playlist-user-id", privateRoutesMiddleware, async (c) => {
    try {
      const user = c.get("user");
      const id = c.req.param("music-playlist-user-id");

      if (!id) {
        return c.json(
          { error: "Music playlist user ID is required" },
          400
        );
      }

      const musicPlaylistUser = await prisma.musicPlaylistUser.findFirst({
        where: {
          id,
          userId: user.id, // Ensure user can only access their own playlists
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          musics: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          natureSounds: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          videos: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      if (!musicPlaylistUser) {
        return c.json(
          { error: "User music playlist not found" },
          404
        );
      }

      return c.json({
        data: musicPlaylistUser,
      });
    } catch (error) {
      console.error("Error fetching user music playlist:", error);
      return c.json(
        { error: "Failed to fetch user music playlist" },
        500
      );
    }
  })
  
  // Update user music playlist
  .patch("/:music-playlist-user-id", privateRoutesMiddleware, zValidator("form", updateMusicPlaylistUserSchema), async (c) => {
    try {
      const user = c.get("user");
      const id = c.req.param("music-playlist-user-id");
      const updates = c.req.valid("form");

      if (!id) {
        return c.json(
          { error: "Music playlist user ID is required" },
          400
        );
      }

      // Verify ownership
      const existingPlaylist = await prisma.musicPlaylistUser.findFirst({
        where: {
          id,
          userId: user.id,
        },
      });

      if (!existingPlaylist) {
        return c.json(
          { error: "User music playlist not found or access denied" },
          404
        );
      }

      const updatedPlaylist = await prisma.musicPlaylistUser.update({
        where: { id },
        data: updates,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          musics: true,
          natureSounds: true,
          videos: true,
        },
      });

      return c.json({
        data: updatedPlaylist,
      });
    } catch (error) {
      console.error("Error updating user music playlist:", error);
      return c.json(
        { error: "Failed to update user music playlist" },
        500
      );
    }
  })
  
  // Delete user music playlist
  .delete("/:music-playlist-user-id", privateRoutesMiddleware, async (c) => {
    try {
      const user = c.get("user");
      const id = c.req.param("music-playlist-user-id");

      if (!id) {
        return c.json(
          { error: "Music playlist user ID is required" },
          400
        );
      }

      // Verify ownership
      const existingPlaylist = await prisma.musicPlaylistUser.findFirst({
        where: {
          id,
          userId: user.id,
        },
      });

      if (!existingPlaylist) {
        return c.json(
          { error: "User music playlist not found or access denied" },
          404
        );
      }

      await prisma.musicPlaylistUser.delete({
        where: { id },
      });

      return c.json({
        success: true,
      });
    } catch (error) {
      console.error("Error deleting user music playlist:", error);
      return c.json(
        { error: "Failed to delete user music playlist" },
        500
      );
    }
  })
  
  // Add music to user playlist
  .patch("/:music-playlist-user-id/add-music", privateRoutesMiddleware, zValidator("json", z.object({
    musicIds: z.array(z.string()).min(1, "At least one music ID is required"),
  })), async (c) => {
    try {
      const user = c.get("user");
      
      const id = c.req.param("music-playlist-user-id");
      const { musicIds } = c.req.valid("json");

      if (!id) {
        return c.json(
          { error: "Music playlist user ID is required" },
          400
        );
      }

      // Verify ownership
      const existingPlaylist = await prisma.musicPlaylistUser.findFirst({
        where: {
          id,
          userId: user.id,
        },
      });

      if (!existingPlaylist) {
        return c.json(
          { error: "User music playlist not found or access denied" },
          404
        );
      }

      // Connect the music tracks
      const musicPlaylistUser = await prisma.musicPlaylistUser.update({
        where: { id },
        data: {
          musics: {
            connect: musicIds.map(musicId => ({ id: musicId })),
          },
          musicOrder: [...existingPlaylist.musicOrder, ...musicIds],
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          musics: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          natureSounds: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          videos: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      return c.json({
        data: musicPlaylistUser,
      });
    } catch (error) {
      console.error("Error adding music to user playlist:", error);
      return c.json(
        { error: "Failed to add music to user playlist" },
        500
      );
    }
  })
  
  // Remove music from user playlist
  .patch("/:music-playlist-user-id/remove-music", privateRoutesMiddleware, zValidator("form", z.object({
    musicId: z.string().min(1, "Music ID is required"),
  })), async (c) => {
    try {
      const user = c.get("user");
      
      const id = c.req.param("music-playlist-user-id");
      const { musicId } = c.req.valid("form");

      if (!id) {
        return c.json(
          { error: "Music playlist user ID is required" },
          400
        );
      }

      // Verify ownership
      const existingPlaylist = await prisma.musicPlaylistUser.findFirst({
        where: {
          id,
          userId: user.id,
        },
      });

      if (!existingPlaylist) {
        return c.json(
          { error: "User music playlist not found or access denied" },
          404
        );
      }

      // Remove from musicOrder
      const newMusicOrder = existingPlaylist.musicOrder.filter((orderMusicId: string) => orderMusicId !== musicId);

      const musicPlaylistUser = await prisma.musicPlaylistUser.update({
        where: { id },
        data: {
          musics: {
            disconnect: { id: musicId },
          },
          musicOrder: newMusicOrder,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          musics: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          natureSounds: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          videos: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      return c.json({
        data: musicPlaylistUser,
      });
    } catch (error) {
      console.error("Error removing music from user playlist:", error);
      return c.json(
        { error: "Failed to remove music from user playlist" },
        500
      );
    }
  })
  
  // Reorder music in user playlist
  .patch("/:music-playlist-user-id/reorder-music", privateRoutesMiddleware, zValidator("json", z.object({
    musicOrder: z.array(z.string()),
  })), async (c) => {
    try {
      const user = c.get("user");
      
      const id = c.req.param("music-playlist-user-id");
      const { musicOrder } = c.req.valid("json");

      if (!id) {
        return c.json(
          { error: "Music playlist user ID is required" },
          400
        );
      }

      // Verify ownership
      const existingPlaylist = await prisma.musicPlaylistUser.findFirst({
        where: {
          id,
          userId: user.id,
        },
      });

      if (!existingPlaylist) {
        return c.json(
          { error: "User music playlist not found or access denied" },
          404
        );
      }

      const musicPlaylistUser = await prisma.musicPlaylistUser.update({
        where: { id },
        data: {
          musicOrder,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          musics: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          natureSounds: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          videos: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      return c.json({
        data: musicPlaylistUser,
      });
    } catch (error) {
      console.error("Error reordering music in user playlist:", error);
      return c.json(
        { error: "Failed to reorder music in user playlist" },
        500
      );
    }
  })
  
  // Add videos to user playlist
  .patch("/:music-playlist-user-id/add-videos", privateRoutesMiddleware, zValidator("json", z.object({
    videoIds: z.array(z.string()).min(1, "At least one video ID is required"),
  })), async (c) => {
    try {
      const user = c.get("user");
      
      const id = c.req.param("music-playlist-user-id");
      const { videoIds } = c.req.valid("json");

      if (!id) {
        return c.json(
          { error: "Music playlist user ID is required" },
          400
        );
      }

      // Verify ownership
      const existingPlaylist = await prisma.musicPlaylistUser.findFirst({
        where: {
          id,
          userId: user.id,
        },
      });

      if (!existingPlaylist) {
        return c.json(
          { error: "User music playlist not found or access denied" },
          404
        );
      }

      // Connect the videos
      const musicPlaylistUser = await prisma.musicPlaylistUser.update({
        where: { id },
        data: {
          videos: {
            connect: videoIds.map(videoId => ({ id: videoId })),
          },
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          musics: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          natureSounds: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          videos: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      return c.json({
        data: musicPlaylistUser,
      });
    } catch (error) {
      console.error("Error adding videos to user playlist:", error);
      return c.json(
        { error: "Failed to add videos to user playlist" },
        500
      );
    }
  })
  
  // Remove video from user playlist
  .patch("/:music-playlist-user-id/remove-video", privateRoutesMiddleware, zValidator("form", z.object({
    videoId: z.string().min(1, "Video ID is required"),
  })), async (c) => {
    try {
      const user = c.get("user");
      
      const id = c.req.param("music-playlist-user-id");
      const { videoId } = c.req.valid("form");

      if (!id) {
        return c.json(
          { error: "Music playlist user ID is required" },
          400
        );
      }

      // Verify ownership
      const existingPlaylist = await prisma.musicPlaylistUser.findFirst({
        where: {
          id,
          userId: user.id,
        },
      });

      if (!existingPlaylist) {
        return c.json(
          { error: "User music playlist not found or access denied" },
          404
        );
      }

      const musicPlaylistUser = await prisma.musicPlaylistUser.update({
        where: { id },
        data: {
          videos: {
            disconnect: { id: videoId },
          },
        },
        include: {
          musics: true,
          natureSounds: true,
          videos: true,
        },
      });

      return c.json({
        data: musicPlaylistUser,
      });
    } catch (error) {
      console.error("Error removing video from user playlist:", error);
      return c.json(
        { error: "Failed to remove video from user playlist" },
        500
      );
    }
  })

  // Add natural sounds to user playlist
  .patch("/:music-playlist-user-id/add-nature-sounds", privateRoutesMiddleware, zValidator("json", z.object({
    natureSoundIds: z.array(z.string()).min(1, "At least one nature sound ID is required"),
  })), async (c) => {
    try {
      const user = c.get("user");

      const id = c.req.param("music-playlist-user-id");
      const { natureSoundIds } = c.req.valid("json");

      if (!id) {
        return c.json(
          { error: "Music playlist user ID is required" },
          400
        );
      }

      // Verify ownership
      const existingPlaylist = await prisma.musicPlaylistUser.findFirst({
        where: {
          id,
          userId: user.id,
        },
      });

      if (!existingPlaylist) {
        return c.json(
          { error: "User music playlist not found or access denied" },
          404
        );
      }

      // Connect the natural sounds
      const musicPlaylistUser = await prisma.musicPlaylistUser.update({
        where: { id },
        data: {
          natureSounds: {
            connect: natureSoundIds.map(natureSoundId => ({ id: natureSoundId })),
          },
          natureSoundOrder: [...existingPlaylist.natureSoundOrder, ...natureSoundIds],
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          musics: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          natureSounds: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          videos: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      return c.json({
        data: musicPlaylistUser,
      });
    } catch (error) {
      console.error("Error adding nature sounds to user playlist:", error);
      return c.json(
        { error: "Failed to add nature sounds to user playlist" },
        500
      );
    }
  })

  // Remove natural sound from user playlist
  .patch("/:music-playlist-user-id/remove-nature-sound", privateRoutesMiddleware, zValidator("form", z.object({
    natureSoundId: z.string().min(1, "Nature sound ID is required"),
  })), async (c) => {
    try {
      const user = c.get("user");

      const id = c.req.param("music-playlist-user-id");
      const { natureSoundId } = c.req.valid("form");

      if (!id) {
        return c.json(
          { error: "Music playlist user ID is required" },
          400
        );
      }

      // Verify ownership
      const existingPlaylist = await prisma.musicPlaylistUser.findFirst({
        where: {
          id,
          userId: user.id,
        },
      });

      if (!existingPlaylist) {
        return c.json(
          { error: "User music playlist not found or access denied" },
          404
        );
      }

      // Remove from natureSoundOrder
      const newNatureSoundOrder = existingPlaylist.natureSoundOrder.filter((orderNatureSoundId: string) => orderNatureSoundId !== natureSoundId);

      const musicPlaylistUser = await prisma.musicPlaylistUser.update({
        where: { id },
        data: {
          natureSounds: {
            disconnect: { id: natureSoundId },
          },
          natureSoundOrder: newNatureSoundOrder,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          musics: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          natureSounds: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          videos: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      return c.json({
        data: musicPlaylistUser,
      });
    } catch (error) {
      console.error("Error removing nature sound from user playlist:", error);
      return c.json(
        { error: "Failed to remove nature sound from user playlist" },
        500
      );
    }
  })

  // Reorder natural sounds in user playlist
  .patch("/:music-playlist-user-id/reorder-nature-sounds", privateRoutesMiddleware, zValidator("json", z.object({
    natureSoundOrder: z.array(z.string()),
  })), async (c) => {
    try {
      const user = c.get("user");

      const id = c.req.param("music-playlist-user-id");
      const { natureSoundOrder } = c.req.valid("json");

      if (!id) {
        return c.json(
          { error: "Music playlist user ID is required" },
          400
        );
      }

      // Verify ownership
      const existingPlaylist = await prisma.musicPlaylistUser.findFirst({
        where: {
          id,
          userId: user.id,
        },
      });

      if (!existingPlaylist) {
        return c.json(
          { error: "User music playlist not found or access denied" },
          404
        );
      }

      const musicPlaylistUser = await prisma.musicPlaylistUser.update({
        where: { id },
        data: {
          natureSoundOrder,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          musics: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          natureSounds: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          videos: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      return c.json({
        data: musicPlaylistUser,
      });
    } catch (error) {
      console.error("Error reordering nature sounds in user playlist:", error);
      return c.json(
        { error: "Failed to reorder nature sounds in user playlist" },
        500
      );
    }
  });

export default app;