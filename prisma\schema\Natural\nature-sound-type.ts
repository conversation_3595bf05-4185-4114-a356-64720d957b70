import { z } from "zod";
import { NatureSoundSchema, NatureSoundPartialSchema } from "@types";
import { NatureSoundCategorySchema, MediaSourceSchema } from "@types";

export const createNatureSoundSchema = NatureSoundSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
}).extend({
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional().default(false),
  description: z.string().optional(),
  category: NatureSoundCategorySchema.array().optional(),
  src: z
    .string()
    .url()
    .transform((value) => (value === "" ? undefined : value))
    .optional(),
  source: MediaSourceSchema.optional(),
  playlistId: z.string().optional().nullable(),
});

export const updateNatureSoundSchema = NatureSoundPartialSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
}).extend({
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional(),
  description: z.string().optional(),
  category: NatureSoundCategorySchema.array().optional(),
  src: z
    .string()
    .url()
    .transform((value) => (value === "" ? undefined : value))
    .optional(),
  source: MediaSourceSchema.optional(),
  playlistId: z.string().optional().nullable(),
});

export type CreateNatureSoundInput = z.infer<typeof createNatureSoundSchema>;
export type UpdateNatureSoundInput = z.infer<typeof updateNatureSoundSchema>;