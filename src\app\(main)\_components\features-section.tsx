'use client';

import { Card, CardContent } from '@/components/ui/card';
import { <PERSON>, Clock, <PERSON><PERSON><PERSON>, Timer } from 'lucide-react';

export const FeaturesSection = () => {

  return (
    <section className="w-full py-12 md:py-16 bg-gradient-to-b from-background via-muted/20 to-background border-t border-muted/30 relative overflow-hidden">
      {/* Subtle floating background elements with CSS animations */}
      <div className="absolute inset-0 z-0 pointer-events-none">
        <div className="absolute top-1/3 right-1/4 h-32 w-32 rounded-full bg-orange-500/3 dark:bg-orange-500/2 blur-2xl animate-float-slow" />
        <div className="absolute bottom-1/4 left-1/4 h-24 w-24 rounded-full bg-red-500/3 dark:bg-red-500/2 blur-2xl animate-float-delayed" />
      </div>

      <style jsx>{`
        @keyframes float-slow {
          0%, 100% { transform: scale(1); opacity: 0.2; }
          50% { transform: scale(1.05); opacity: 0.3; }
        }
        @keyframes float-delayed {
          0%, 100% { transform: scale(1.05); opacity: 0.3; }
          50% { transform: scale(1); opacity: 0.4; }
        }
        .animate-float-slow {
          animation: float-slow 8s ease-in-out infinite;
        }
        .animate-float-delayed {
          animation: float-delayed 10s ease-in-out infinite 2s;
        }
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fade-in-up {
          animation: fade-in-up 0.6s ease-out forwards;
        }
        .animate-fade-in-up-delay-1 {
          animation: fade-in-up 0.6s ease-out 0.1s forwards;
          opacity: 0;
        }
        .animate-fade-in-up-delay-2 {
          animation: fade-in-up 0.6s ease-out 0.2s forwards;
          opacity: 0;
        }
        .animate-fade-in-up-delay-3 {
          animation: fade-in-up 0.6s ease-out 0.3s forwards;
          opacity: 0;
        }
        .animate-fade-in-up-delay-4 {
          animation: fade-in-up 0.6s ease-out 0.4s forwards;
          opacity: 0;
        }
        .animate-fade-in-up-delay-5 {
          animation: fade-in-up 0.6s ease-out 0.5s forwards;
          opacity: 0;
        }
        .animate-fade-in-up-delay-6 {
          animation: fade-in-up 0.6s ease-out 0.6s forwards;
          opacity: 0;
        }
      `}</style>

      <div className="container mx-auto px-4 max-w-6xl relative z-10">
        <div className="flex flex-col items-center text-center mb-10 animate-fade-in-up">
          <div className="inline-flex items-center gap-2 px-3 py-1.5 mb-4 rounded-full bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-950/30 dark:to-red-950/30 text-orange-600 dark:text-orange-400 text-sm font-medium border border-orange-200/60 dark:border-orange-800/40 hover:scale-105 transition-transform duration-200 animate-fade-in-up-delay-1">
            <Timer className="h-4 w-4" />
            <span>Why Choose Us</span>
          </div>

          <h2
            className="text-2xl md:text-3xl lg:text-4xl font-bold tracking-tight text-foreground mb-4 animate-fade-in-up-delay-2"
            style={{
              fontFamily: 'var(--font-geist-sans)',
              letterSpacing: '-0.025em',
            }}
          >
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 dark:from-slate-100 dark:via-slate-200 dark:to-slate-300">
              Why Choose{' '}
            </span>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 font-extrabold">
              Pomodoro 365?
            </span>
          </h2>

          <p
            className="text-base text-muted-foreground max-w-2xl leading-relaxed animate-fade-in-up-delay-3"
            style={{ fontFamily: 'var(--font-geist-sans)' }}
          >
            Boost your productivity with scientifically-proven techniques, stunning backgrounds, and immersive music.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
          <div className="animate-fade-in-up-delay-4">
            <FeatureCard
              icon={<Video className="h-5 w-5" />}
              title="Focus-Enhancing Videos"
              description="Immersive backgrounds to create the perfect environment for deep work and sustained concentration."
              colorScheme="blue"
            />
          </div>

          <div className="animate-fade-in-up-delay-5">
            <FeatureCard
              icon={<Clock className="h-5 w-5" />}
              title="Customizable Timers"
              description="Tailor work and break durations to match your personal productivity rhythm and workflow preferences."
              colorScheme="emerald"
            />
          </div>

          <div className="animate-fade-in-up-delay-6">
            <FeatureCard
              icon={<BarChart className="h-5 w-5" />}
              title="Productivity Tracking"
              description="Proven time management technique to enhance focus, prevent burnout, and track your productivity growth."
              colorScheme="orange"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  colorScheme: 'blue' | 'emerald' | 'orange';
}

const FeatureCard = ({ icon, title, description, colorScheme }: FeatureCardProps) => {
  const colorClasses = {
    blue: {
      bg: 'bg-gradient-to-br from-blue-50/80 to-blue-100/40 dark:from-blue-950/15 dark:to-blue-900/10',
      border: 'border-blue-200/50 dark:border-blue-800/20',
      icon: 'bg-blue-500/15 text-blue-600 dark:text-blue-400',
      iconBg: 'bg-blue-500',
      title: 'text-blue-700 dark:text-blue-300',
      accent: 'bg-blue-400/30 dark:bg-blue-500/20',
      hover: 'group-hover:bg-blue-500/5',
      shadow: 'group-hover:shadow-blue-500/10'
    },
    emerald: {
      bg: 'bg-gradient-to-br from-emerald-50/80 to-emerald-100/40 dark:from-emerald-950/15 dark:to-emerald-900/10',
      border: 'border-emerald-200/50 dark:border-emerald-800/20',
      icon: 'bg-emerald-500/15 text-emerald-600 dark:text-emerald-400',
      iconBg: 'bg-emerald-500',
      title: 'text-emerald-700 dark:text-emerald-300',
      accent: 'bg-emerald-400/30 dark:bg-emerald-500/20',
      hover: 'group-hover:bg-emerald-500/5',
      shadow: 'group-hover:shadow-emerald-500/10'
    },
    orange: {
      bg: 'bg-gradient-to-br from-orange-50/80 to-red-50/40 dark:from-orange-950/15 dark:to-red-900/10',
      border: 'border-orange-200/50 dark:border-orange-800/20',
      icon: 'bg-orange-500/15 text-orange-600 dark:text-orange-400',
      iconBg: 'bg-gradient-to-r from-orange-500 to-red-500',
      title: 'text-orange-700 dark:text-orange-300',
      accent: 'bg-orange-400/30 dark:bg-orange-500/20',
      hover: 'group-hover:bg-orange-500/5',
      shadow: 'group-hover:shadow-orange-500/10'
    }
  };

  const colors = colorClasses[colorScheme];

  return (
    <div className="group h-full hover:-translate-y-0.5 transition-transform duration-200 ease-out">
      <Card className={`overflow-hidden ${colors.bg} ${colors.border} h-full relative group-hover:shadow-md ${colors.shadow} transition-all duration-300 backdrop-blur-sm`}>
        <CardContent className="p-0 h-full">
          <div className="p-4 flex flex-col items-center text-center h-full relative">
            {/* Subtle background overlay */}
            <div className={`absolute inset-0 ${colors.hover} opacity-0 group-hover:opacity-100 transition-opacity duration-300`} />

            {/* Icon container */}
            <div className={`flex items-center justify-center w-10 h-10 rounded-lg mb-3 ${colors.iconBg} text-white shadow-sm relative z-10 ring-1 ring-white/20 hover:scale-105 transition-transform duration-200`}>
              {icon}
            </div>

            {/* Title */}
            <h3
              className={`text-base font-semibold mb-2 ${colors.title} relative z-10`}
              style={{ fontFamily: 'var(--font-geist-sans)' }}
            >
              {title}
            </h3>

            {/* Description */}
            <p className="text-muted-foreground leading-relaxed text-sm flex-grow relative z-10" style={{ fontFamily: 'var(--font-geist-sans)' }}>
              {description}
            </p>
          </div>

          {/* Bottom accent line */}
          <div
            className={`h-0.5 w-full ${colors.accent} scale-x-30 group-hover:scale-x-100 transition-transform duration-300 ease-out origin-left`}
          />
        </CardContent>
      </Card>
    </div>
  );
};