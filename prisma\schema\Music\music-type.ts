import { z } from "zod";
import { MusicSchema, MusicPartialSchema } from "@types";
import { MusicGenreSchema, MediaSourceSchema } from "@types";

export const createMusicSchema = MusicSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
}).extend({
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional().default(false),
  isCopyright: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional().default(false),
  src: z
    .string()
    .url()
    .transform((value) => (value === "" ? undefined : value))
    .optional(),
  source: MediaSourceSchema.optional(),
  rating: z
    .union([
      z.number().min(0).max(5),
      z.string().transform((val) => {
        const num = parseFloat(val);
        return isNaN(num) ? undefined : num;
      })
    ])
    .optional(),
  genres: MusicGenreSchema["array"]().optional(),
  playlistId: z.string().optional().nullable(),
});

export const updateMusicSchema = MusicPartialSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
}).extend({
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional(),
  isCopyright: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional(),
  src: z
    .string()
    .url()
    .transform((value) => (value === "" ? undefined : value))
    .optional(),
  source: MediaSourceSchema.optional(),
  rating: z
    .union([
      z.number().min(0).max(5),
      z.string().transform((val) => {
        const num = parseFloat(val);
        return isNaN(num) ? undefined : num;
      })
    ])
    .optional(),
  genres: MusicGenreSchema["array"]().optional(),
  playlistId: z.string().optional().nullable(),
});

export type CreateMusicInput = z.infer<typeof createMusicSchema>;
export type UpdateMusicInput = z.infer<typeof updateMusicSchema>;