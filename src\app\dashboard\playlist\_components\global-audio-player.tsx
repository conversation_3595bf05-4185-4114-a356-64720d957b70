"use client"

import { useEffect, useRef, useCallback, useState } from "react"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  X,
  Music2,
  Waves
} from "lucide-react"
import { useAudioStore } from "@/lib/audio-store"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"

export function GlobalAudioPlayer() {
  const {
    globalPlayer,
    stopGlobalPlayer,
  } = useAudioStore()

  const audioRef = useRef<HTMLAudioElement | null>(null)

  // Local state for audio playback (like the working admin player)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(80)
  const [isMuted, setIsMuted] = useState(false)

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Handle time update event (using local state like admin player)
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime)
    }
  }, [])

  // Handle loaded metadata event (using local state like admin player)
  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration)
    }
  }, [])

  // Handle play/pause toggle (using local state like admin player)
  const handlePlayPause = useCallback(() => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      audioRef.current.play().catch(error => {
        console.error("Play failed:", error)
      })
    }
  }, [isPlaying])

  // Handle volume toggle
  const handleVolumeToggle = useCallback(() => {
    setIsMuted(!isMuted)
  }, [isMuted])

  // Handle seeking in the timeline
  const handleSeek = useCallback((value: number[]) => {
    const newTime = value[0]
    if (audioRef.current) {
      audioRef.current.currentTime = newTime
      setCurrentTime(newTime)
    }
  }, [])

  // Handle close player
  const handleClose = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
    }
    stopGlobalPlayer()
  }, [stopGlobalPlayer])

  // Create/update audio element and set up event listeners (like admin player)
  useEffect(() => {
    if (!globalPlayer.currentTrack?.src) {
      setIsPlaying(false)
      return
    }

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      audioRef.current = new Audio()

      // Set up event listeners for the audio element (using local state)
      audioRef.current.addEventListener("play", () => setIsPlaying(true))
      audioRef.current.addEventListener("pause", () => setIsPlaying(false))
      audioRef.current.addEventListener("ended", () => {
        setIsPlaying(false)
        stopGlobalPlayer()
      })
      audioRef.current.addEventListener("timeupdate", handleTimeUpdate)
      audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata)
    }

    // Update audio source if it changed
    if (audioRef.current.src !== globalPlayer.currentTrack.src) {
      audioRef.current.src = globalPlayer.currentTrack.src
      audioRef.current.load()
    }

    // Play the audio
    const playPromise = audioRef.current.play()

    // Handle autoplay restrictions
    if (playPromise !== undefined) {
      playPromise.catch(error => {
        console.error("Autoplay prevented:", error)
        setIsPlaying(false)
      })
    }

    // Update volume & muted state
    audioRef.current.volume = isMuted ? 0 : volume / 100

    // Cleanup function
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("play", () => setIsPlaying(true))
        audioRef.current.removeEventListener("pause", () => setIsPlaying(false))
        audioRef.current.removeEventListener("ended", () => {
          setIsPlaying(false)
          stopGlobalPlayer()
        })
        audioRef.current.removeEventListener("timeupdate", handleTimeUpdate)
        audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata)
        audioRef.current.pause()
      }
    }
  }, [globalPlayer.currentTrack?.src, stopGlobalPlayer, isMuted, volume, handleTimeUpdate, handleLoadedMetadata])

  // Update volume when it changes (like admin player)
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume / 100
    }
  }, [volume, isMuted])

  if (!globalPlayer.currentTrack) {
    return null
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 100, opacity: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="fixed bottom-0 left-0 right-0 z-[9999] bg-background/95 backdrop-blur-sm border-t border-border shadow-lg"
      >
        <div className="w-full max-w-[120rem] mx-auto p-4">
          <div className="flex items-center gap-4">
            {/* Play/Pause Button */}
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-10 w-10 rounded-full shrink-0 transition-all duration-200",
                isPlaying
                  ? "bg-gradient-to-r from-orange-500 to-rose-500 text-white hover:from-orange-600 hover:to-rose-600"
                  : "hover:bg-muted"
              )}
              onClick={handlePlayPause}
            >
              {isPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4 ml-0.5" />
              )}
            </Button>

            {/* Track Icon */}
            <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 flex items-center justify-center shrink-0">
              {globalPlayer.currentTrack.type === "music" ? (
                <Music2 className="h-4 w-4 text-orange-600 dark:text-orange-400" />
              ) : (
                <Waves className="h-4 w-4 text-orange-600 dark:text-orange-400" />
              )}
            </div>

            {/* Track Info */}
            <div className="flex-1 min-w-0">
              <div className="truncate font-medium text-sm">
                {globalPlayer.currentTrack.title}
              </div>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>{formatTime(currentTime)}</span>
                <span>/</span>
                <span>{formatTime(duration)}</span>
                {globalPlayer.currentTrack.type === "music" && globalPlayer.currentTrack.genres && globalPlayer.currentTrack.genres.length > 0 && (
                  <>
                    <span>•</span>
                    <Badge variant="secondary" className="text-xs">
                      {globalPlayer.currentTrack.genres[0]}
                    </Badge>
                  </>
                )}
                {globalPlayer.currentTrack.type === "nature-sound" && globalPlayer.currentTrack.category && globalPlayer.currentTrack.category.length > 0 && (
                  <>
                    <span>•</span>
                    <Badge variant="secondary" className="text-xs">
                      {globalPlayer.currentTrack.category[0]}
                    </Badge>
                  </>
                )}
              </div>
            </div>

            {/* Progress Bar */}
            <div className="hidden md:flex flex-1 max-w-48 items-center">
              <Slider
                value={[currentTime]}
                min={0}
                max={duration || 100}
                step={0.1}
                onValueChange={handleSeek}
                className="cursor-pointer"
              />
            </div>

            {/* Volume Controls */}
            <div className="flex items-center gap-2 shrink-0">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleVolumeToggle}
              >
                {isMuted ? (
                  <VolumeX className="h-4 w-4" />
                ) : (
                  <Volume2 className="h-4 w-4" />
                )}
              </Button>
              <Slider
                value={[isMuted ? 0 : volume]}
                min={0}
                max={100}
                step={1}
                onValueChange={(value) => {
                  setVolume(value[0])
                  if (value[0] > 0 && isMuted) {
                    setIsMuted(false)
                  }
                }}
                className="w-20"
              />
            </div>

            {/* Close Button */}
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Mobile Progress Bar */}
          <div className="md:hidden mt-3">
            <Slider
              value={[currentTime]}
              min={0}
              max={duration || 100}
              step={0.1}
              onValueChange={handleSeek}
              className="cursor-pointer"
            />
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
